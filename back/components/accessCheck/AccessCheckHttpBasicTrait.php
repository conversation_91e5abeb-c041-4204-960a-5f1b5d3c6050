<?php

declare(strict_types=1);

namespace app\back\components\accessCheck;

use app\back\components\BaseAuthAccess;
use app\back\components\Container;
use app\back\components\Request;

trait AccessCheckHttpBasicTrait
{
    final public function checkAccess(Container $container, string $action, Request $request): bool
    {
        $auth = $container->get(BaseAuthAccess::class);
        $auth->employeeId(); // Access check happened here

        return true;
    }
}
