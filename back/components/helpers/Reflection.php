<?php

declare(strict_types=1);

namespace app\back\components\helpers;

class Reflection
{
    public static function getSimpleType(\ReflectionProperty $prop): \ReflectionNamedType
    {
        $type = $prop->getType();

        if ($type instanceof \ReflectionUnionType) {
            foreach ($type->getTypes() as $t) {
                if ($t->getName() !== 'null') {
                    return $t;
                }
            }
        }

        return $type;
    }
}
