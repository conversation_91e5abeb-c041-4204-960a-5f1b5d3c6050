<?php

declare(strict_types=1);

namespace app\back\components\helpers;

class File
{
    public static function createDirectoryIfNotExists(string $path, int $directoryMode = 0777, bool $fileNameToDir = false): bool
    {
        if (is_dir($path)) {
            return true;
        }

        $parts = explode('/', $path);

        if ($fileNameToDir) {
            array_pop($parts);
        }

        $path = '';
        foreach ($parts as $part) {
            $path .= $part . '/';

            if (is_dir($path)) {
                continue;
            }

            if (is_file($path)) {
                return false;
            }

            try {
                $result = mkdir(directory: $path, recursive: true);
            } catch (\ErrorException $e) {
                // Skip concurrently created directory
                if (str_contains($e->getMessage(), 'File exists')) {
                    $result = true;
                }
            }

            if (!is_dir($path)) {
                return false;
            }

            if ($result === false) {
                return false;
            }

            chmod($path, $directoryMode);
        }

        return true;
    }

    public static function pathPermissionsOctal(string $path): string
    {
        return substr(sprintf('%o', fileperms($path)), -4);
    }
}
