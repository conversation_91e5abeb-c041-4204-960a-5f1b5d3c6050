<?php

declare(strict_types=1);

namespace app\back\components;

/**
 * @see RuntimeCachedTrait::runtimeCached()
 * used for tests to clean static cache in objects after each test
 */
class RuntimeCache
{
    private static array $cache = [];
    private static array $usages = [];

    public static function &get(string $name, \Closure $set): mixed
    {
        if (!array_key_exists($name, self::$cache)) {
            self::$cache[$name] = $set();
        } else {
            self::$usages[$name] = (self::$usages[$name] ?? 0) + 1;
        }
        return self::$cache[$name];
    }

    public static function set(string $name, mixed $value): mixed
    {
        return self::$cache[$name] = $value;
    }

    public static function clear(): void
    {
        self::$cache = [];
        self::$usages = [];
    }

    /** only for debug in tests */
    public static function getUsagesStat(): string
    {
        $usages = array_merge(array_fill_keys(array_keys(self::$cache), 0), self::$usages);
        return implode("\n", array_map(static fn($name) => "$name => " . $usages[$name], array_keys($usages)));
    }
}
