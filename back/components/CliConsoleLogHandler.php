<?php

declare(strict_types=1);

namespace app\back\components;

use app\back\components\exceptions\ErrorFromLogException;
use Monolog\Formatter\FormatterInterface;
use Monolog\Formatter\LineFormatter;
use Monolog\Logger;

class CliConsoleLogHandler extends BaseLogHandler
{
    private ?string $lastMessage = null;
    private int $lastMessageRepeatedTimes = 0;
    private bool $sentryErrorHandling = true;

    public function disableSentryErrorHandling(): void
    {
        $this->sentryErrorHandling = false;
    }

    protected function write(array $record): void
    {
        $message = (string) $record['message'];
        $formatted = $this->getFormatter()->format($record);
        $stream = $record['level'] >= Logger::ERROR ? \STDERR : \STDOUT;

        if ($message === $this->lastMessage) {
            $this->lastMessageRepeatedTimes++;
            Console::rewrite("{$formatted} | Repeated: {$this->lastMessageRepeatedTimes} time(s)", $stream, lines: substr_count($this->lastMessage, "\n") + 1);
        } else {
            $this->lastMessage = $message;
            $this->lastMessageRepeatedTimes = 1;
            Console::write($formatted, $stream);
        }

        if ($stream === \STDERR && $this->sentryErrorHandling) {
            ErrorBase::sentryCaptureException(new ErrorFromLogException($message));
        }
    }

    public function getDefaultFormatter(): FormatterInterface
    {
        return new class ("%datetime_color%%datetime%%color_end% %level_color%%level_name%%color_end% %message% %stacktrace%", 'H:i:s', true, true) extends LineFormatter {
            public function format(array $record): string
            {
                $levelColor = Console::logLevelColor($record['level']);
                if (isset($record['context']['exception'])) {
                    $record['stacktrace'] = "\n\t" . str_replace("\n", "\n\t", $record['context']['exception']->getTraceAsString());
                } elseif (isset($record['context']['file'])) {
                    $record['stacktrace'] = "in {$record['context']['file']} on line {$record['context']['line']}";
                } else {
                    $record['stacktrace'] = '';
                }
                $record['level_color'] = Console::formatStart(Console::FG_BLACK, $levelColor);
                $record['datetime_color'] = Console::formatStart(Console::FG_GREY);
                $record['color_end'] = Console::formatReset();
                return parent::format($record);
            }
        };
    }
}
