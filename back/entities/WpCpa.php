<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\CurrencyFormatValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\MoneyValidator;

class WpCpa extends BaseEntity
{
    public const int STATUS_HOLD = 1;
    public const int STATUS_APPROVE = 2;
    public const int STATUS_REJECT = 3;

    public const array STATUSES = [
        self::STATUS_HOLD => 'Hold',
        self::STATUS_APPROVE => 'Approve',
        self::STATUS_REJECT => 'Reject',
    ];

    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[MoneyValidator(2, -10000)]
    public string $sum;
    #[CurrencyFormatValidator]
    public string $currency;
    #[MoneyValidator(2, -10000)]
    public ?string $sum_usd;
    #[MoneyValidator(2, -10000)]
    public ?string $sum_rub;
    #[IntInArrayValidator(self::STATUSES)]
    public int $status;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[DateValidator]
    public ?string $day;
    #[MoneyValidator(2, -10000)]
    public ?string $sum_eur;
}
