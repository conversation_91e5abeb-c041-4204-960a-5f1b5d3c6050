<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\DateTimeImmutableWithMicroseconds;
use app\back\components\helpers\Str;
use app\back\components\PaymentTypeResolver;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CurrencyFormatValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\FilterValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\IpValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringLimitedValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UuidValidator;
use app\back\repositories\UserTransactions;

class UserTransaction extends BaseEntity
{
    // SMEN
    public const int STATUS_NEW = 1;
    public const int STATUS_IN_PROCESS = 2;
    public const int STATUS_SUCCESS = 3;
    public const int STATUS_FAIL = 4;
    public const int STATUS_MANUAL = 5;
    public const int STATUS_PARTIAL = 6;
    public const int STATUS_USER_CANCELLED = 7;
    public const int STATUS_USER_AGREEMENT = 8;
    public const int STATUS_CANCELLED = 9;

    // YS
    public const int STATUS_PENDING = 105;
    public const int STATUS_VOIDED = 106;
    public const int STATUS_REFUNDED = 107;
    public const int STATUS_TRY = 108;
    public const int STATUS_REJECTED = 109;
    public const int STATUS_CHARGED = 110;
    public const int STATUS_REVERSAL = 111;

    public const array STATUSES = [
        self::STATUS_NEW => 'New',
        self::STATUS_IN_PROCESS => 'Process',
        self::STATUS_SUCCESS => 'Success',
        self::STATUS_FAIL => 'Fail',
        self::STATUS_MANUAL => 'Manual',
        self::STATUS_PARTIAL => 'Partial',
        self::STATUS_USER_CANCELLED => 'Canceled (by user)',
        self::STATUS_USER_AGREEMENT => 'Agreement (by user)',
        self::STATUS_CANCELLED => 'Canceled',
        self::STATUS_PENDING => 'Pending',
        self::STATUS_VOIDED => 'Voided',
        self::STATUS_REFUNDED => 'Refunded',
        self::STATUS_TRY => 'Try',
        self::STATUS_REJECTED => 'Rejected',
        self::STATUS_CHARGED => 'Charged',
        self::STATUS_REVERSAL => 'Reversal',
    ];

    // SMEN and common
    public const int OP_IN = 1;
    public const int OP_OUT = 2;
    public const int OP_ADMIN_IN_PRIZE = 3;
    public const int OP_ADMIN_IN_COMPENSATION = 4;
    public const int OP_ADMIN_OUT_MANUAL = 10;
    public const int OP_EXCHANGE_POINT = 11;
    public const int OP_OUT_BUY_LOTTERY_TICKETS = 12;
    public const int OP_BUY_LOOTBOX = 13;
    public const int OP_WHEEL_FORTUNE_SPIN = 14;
    public const int OP_BUY_TALISMAN = 15;
    public const int OP_BUY_BENEFITS = 16;
    public const int OP_DAILY_CASH_BACK_PRIZE = 17;
    public const int OP_REGISTER_TOURNAMENT_BLITZ = 18;
    public const int OP_UNREGISTER_TOURNAMENT_BLITZ = 19;
    public const int OP_REBUY_TOURNAMENT_BLITZ = 20;
    public const int OP_CANCELED_TOURNAMENT_BLITZ = 21;
    public const int OP_LOTTERY_EXTRA_ATTEMPT = 22;
    public const int OP_BUY_BONUS = 24;
    public const int OP_INSURANCE_PAY = 25;
    public const int OP_STRIP = 26;
    public const int OP_PAYMENT_BONUS = 27;
    public const int OP_STRIP_BONUS = 28;
    public const int OP_REAL_BALANCE_PRIZE = 30;
    public const int OP_BONUS_BALANCE_PRIZE = 31;
    public const int OP_RESET_BONUS_BALANCE = 32;
    public const int OP_BS_BONUS_LOYALTY = 33;
    public const int OP_BS_BONUS_MANUAL_ACTIVATION = 34;
    public const int OP_BS_BONUS_PROGRESSIVE = 35;
    public const int OP_BS_BONUS_PROMO_CODE = 36;
    public const int OP_BS_BONUS_REGISTER = 37;
    public const int OP_BS_BONUS_SINGLE = 38;
    public const int OP_BS_BONUS_WHITE_LIST = 39;
    public const int OP_BS_REAL_LOYALTY = 40;
    public const int OP_BS_REAL_MANUAL_ACTIVATION = 41;
    public const int OP_BS_REAL_PROGRESSIVE = 42;
    public const int OP_BS_REAL_PROMO_CODE = 43;
    public const int OP_BS_REAL_REGISTER = 44;
    public const int OP_BS_REAL_SINGLE = 45;
    public const int OP_BS_REAL_WHITE_LIST = 46;
    public const int OP_BS_REAL_MANUAL_ASSIGN = 47;
    public const int OP_BS_BONUS_MANUAL_ASSIGN = 48;
    public const int OP_BS_WIN_BACK = 49;
    public const int OP_REFUND = 50;
    public const int OP_PAYOUT_OFFER_BONUS = 51;
    public const int OP_PAYOUT_OFFER_REAL_WITHDRAWAL = 52;
    public const int OP_BS_REAL_PERSONAL_ASSIGN = 53;
    public const int OP_BS_BONUS_PERSONAL_ASSIGN = 54;
    public const int OP_BETTING_BALANCE_WAGERED = 55;
    public const int OP_BETTING_CANCEL_BALANCE_WAGERING = 56;
    public const int OP_PAY_SYSTEM_BONUS = 57;
    public const int OP_REFERRAL_PROGRAM = 58;
    public const int OP_EXPIRE_BONUS_BALANCE = 59;

    // SLOTTY
    public const int OP_BONUS_REDEEM = 101;
    public const int OP_BONUS_LOST = 102;

    // GI
    public const int OP_BONUS_TRANSFER = 103;
    public const int OP_SCRATCH_LOTTERY_TICKET = 104;
    public const int OP_FREE_SPINS = 105;
    public const int OP_DORMANCY_FEE = 107; // автосписывание денег с баланса неактивного юзера
    public const int OP_DORMANCY_FINAL = 108; // финальное автосписывание денег у неактивного юзера, так, что на балансе становится 0
    public const int OP_DE_FEE = 109; // @noterdin немцы поменяли систему налогообложения, потому с немецких пользователей будут дополнительное списание, когда они будут делать ставки казиношные то есть прям совсем новые списания, таких у нас раньше не было
    public const int OP_FREE_BET = 111;
    public const int OP_PLAY_REWARD = 113;
    public const int OP_LOOTBOX_PRIZE = 114;
    // MB and YS
    public const int OP_OUT_COMMISSION = 106;
    public const int OP_WAGER_ABUSE_PENALTY = 112;
    public const int OP_DEP_UNLOCK = 117;

    // Universal
    public const int OP_WHEEL_FORTUNE_PRIZE = 110;
    public const int OP_STASH_BONUS = 115;
    public const int OP_GAME_PROVIDER_BONUS = 116;
    public const int OP_ACTIVE_PLAYER_BONUS = 118;
    public const int OP_OPERATOR_TIP = 119;
    public const int OP_TOURNAMENT_BONUS = 120;
    public const int OP_TOURNAMENT_FUN_BONUS = 121;
    public const int OP_FUN_REWARD = 122;

    public const array OPERATIONS = [
        self::OP_IN => 'In',
        self::OP_OUT => 'Out',
        self::OP_ADMIN_IN_PRIZE => 'Admin in prize',
        self::OP_ADMIN_IN_COMPENSATION => 'Admin in compensation',
        self::OP_ADMIN_OUT_MANUAL => 'Admin out manual',
        self::OP_EXCHANGE_POINT => 'Exchange point',
        self::OP_OUT_BUY_LOTTERY_TICKETS => 'Out buy lottery tickets',
        self::OP_BUY_LOOTBOX => 'Buy lootbox',
        self::OP_WHEEL_FORTUNE_SPIN => 'Wheel fortune spin',
        self::OP_WHEEL_FORTUNE_PRIZE => 'Wheel fortune prize',
        self::OP_DAILY_CASH_BACK_PRIZE => 'Daily cash back prize',
        self::OP_BUY_TALISMAN => 'Buy talisman',
        self::OP_REGISTER_TOURNAMENT_BLITZ => 'Register tournament blitz',
        self::OP_UNREGISTER_TOURNAMENT_BLITZ => 'Unregister tournament blitz',
        self::OP_REBUY_TOURNAMENT_BLITZ => 'Rebuy tournament blitz',
        self::OP_CANCELED_TOURNAMENT_BLITZ => 'Canceled tournament blitz',
        self::OP_BUY_BENEFITS => 'Buy benefits',
        self::OP_LOTTERY_EXTRA_ATTEMPT => 'Lottery extra attempt',
        self::OP_INSURANCE_PAY => 'Insurance pay',
        self::OP_STRIP => 'Strip',
        self::OP_PAYMENT_BONUS => 'Payment bonus',
        self::OP_STRIP_BONUS => 'Strip bonus',
        self::OP_REAL_BALANCE_PRIZE => 'Real balance prize',
        self::OP_BONUS_BALANCE_PRIZE => 'Bonus balance prize',
        self::OP_RESET_BONUS_BALANCE => 'Reset bonus balance',
        self::OP_EXPIRE_BONUS_BALANCE => 'Expire bonus balance',
        self::OP_BS_BONUS_LOYALTY => 'BS loyalty (bonus)',
        self::OP_BS_BONUS_MANUAL_ACTIVATION => 'BS manual activation (bonus)',
        self::OP_BS_BONUS_PROGRESSIVE => 'BS bonus progressive (bonus)',
        self::OP_BS_BONUS_PROMO_CODE => 'BS promo code (bonus)',
        self::OP_BS_BONUS_REGISTER => 'BS register (bonus)',
        self::OP_BS_BONUS_SINGLE => 'BS single (bonus)',
        self::OP_BS_BONUS_WHITE_LIST => 'BS white list (bonus)',
        self::OP_BS_BONUS_MANUAL_ASSIGN => 'BS manual assign (bonus)',
        self::OP_BS_BONUS_PERSONAL_ASSIGN => 'BS personal assign (bonus)',
        self::OP_BS_REAL_LOYALTY => 'BS loyalty (real)',
        self::OP_BS_REAL_MANUAL_ACTIVATION => 'BS manual activation (real)',
        self::OP_BS_REAL_PROGRESSIVE => 'BS bonus progressive (real)',
        self::OP_BS_REAL_PROMO_CODE => 'BS promo code (real)',
        self::OP_BS_REAL_REGISTER => 'BS register (real)',
        self::OP_BS_REAL_SINGLE => 'BS single (real',
        self::OP_BS_REAL_WHITE_LIST => 'BS white list (real)',
        self::OP_BS_REAL_MANUAL_ASSIGN => 'BS manual assign (real)',
        self::OP_BS_REAL_PERSONAL_ASSIGN => 'BS personal assign (real)',
        self::OP_BS_WIN_BACK => 'BS win back',
        self::OP_BONUS_TRANSFER => 'Bonus transfer',
        self::OP_SCRATCH_LOTTERY_TICKET => 'Scratch lottery ticket',
        self::OP_BONUS_REDEEM => 'Bonus redeem',
        self::OP_FREE_SPINS => 'Free spins',
        self::OP_REFUND => 'Distributed refund',
        self::OP_PAYOUT_OFFER_BONUS => 'Payout offer bonus',
        self::OP_PAYOUT_OFFER_REAL_WITHDRAWAL => 'Payout offer withdrawal',
        self::OP_OUT_COMMISSION => 'Out commission',
        self::OP_DORMANCY_FEE => 'Dormancy fee',
        self::OP_DORMANCY_FINAL => 'Dormancy final',
        self::OP_DE_FEE => 'DE fee',
        self::OP_FREE_BET => 'Free bet',
        self::OP_WAGER_ABUSE_PENALTY => 'Wager abuse penalty',
        self::OP_PLAY_REWARD => 'Play reward',
        self::OP_BETTING_BALANCE_WAGERED => 'Betting balance wagered',
        self::OP_BETTING_CANCEL_BALANCE_WAGERING => 'Betting cancel balance wagering',
        self::OP_PAY_SYSTEM_BONUS => 'Pay system bonus',
        self::OP_LOOTBOX_PRIZE => 'Lootbox prize',
        self::OP_BUY_BONUS => 'Buy bonus',
        self::OP_STASH_BONUS => 'Stash bonus',
        self::OP_GAME_PROVIDER_BONUS => 'Game provider bonus',
        self::OP_DEP_UNLOCK => 'Dep unlock',
        self::OP_REFERRAL_PROGRAM => 'Referral program',
        self::OP_ACTIVE_PLAYER_BONUS => 'Active player bonus',
        self::OP_OPERATOR_TIP => 'Operator tip',
        self::OP_TOURNAMENT_BONUS => 'Tournament bonus',
        self::OP_TOURNAMENT_FUN_BONUS => 'Tournament (fun) bonus',
        self::OP_FUN_REWARD => 'Fun reward',
    ];

    public const int DIR_IN = 1;
    public const int DIR_OUT = -1;

    public const array DIRS = [
        self::DIR_IN => 'In',
        self::DIR_OUT => 'Out',
    ];

    public const int BALANCE_TYPE_REAL = 1;
    public const int BALANCE_TYPE_BONUS = 2;
    public const int BALANCE_TYPE_OTHER = 10;

    public const array BALANCE_TYPES = [
        self::BALANCE_TYPE_REAL => 'Real',
        self::BALANCE_TYPE_BONUS => 'Bonus',
        self::BALANCE_TYPE_OTHER => 'Other',
    ];

    public const int EXT_TYPE_NORMAL = 1;
    public const int EXT_TYPE_PRIZE = 2;
    public const int EXT_TYPE_COMPENSATION = 3;
    public const int EXT_TYPE_EXCHANGE = 4;
    public const int EXT_TYPE_CASH_BACK = 5;
    public const int EXT_TYPE_PROMO_CODE = 6;
    public const int EXT_TYPE_AUTO = 7;
    public const int EXT_TYPE_TICKET = 8;
    public const int EXT_TYPE_TRANSFER = 9;
    public const int EXT_TYPE_OTHER = 100;

    public const array EXT_TYPES = [
        self::EXT_TYPE_NORMAL => 'Normal',
        self::EXT_TYPE_PRIZE => 'Prize',
        self::EXT_TYPE_COMPENSATION => 'Compensation',
        self::EXT_TYPE_EXCHANGE => 'Exchange',
        self::EXT_TYPE_CASH_BACK => 'Cash back',
        self::EXT_TYPE_PROMO_CODE => 'Promo code',
        self::EXT_TYPE_AUTO => 'Auto',
        self::EXT_TYPE_TICKET => 'Ticket',
        self::EXT_TYPE_TRANSFER => 'Transfer',
        self::EXT_TYPE_OTHER => 'Other',
    ];

    public const array EXT_TYPES_FOR_BONUS_RATIO = [
        self::EXT_TYPE_PRIZE,
        self::EXT_TYPE_COMPENSATION,
        self::EXT_TYPE_EXCHANGE,
        self::EXT_TYPE_CASH_BACK,
        self::EXT_TYPE_PROMO_CODE,
        self::EXT_TYPE_AUTO,
        self::EXT_TYPE_TICKET,
        self::EXT_TYPE_OTHER,
    ];

    public const string AMOUNT_USD = 'amount_usd';
    public const string AMOUNT_RUB = 'amount_rub';
    public const string AMOUNT_EUR = 'amount_eur';
    public const string AMOUNT_ORIG = 'amount_orig';

    public const array AMOUNT_COLUMN_BY_CURRENCY = [
        Rate::USD => self::AMOUNT_USD,
        Rate::RUB => self::AMOUNT_RUB,
        Rate::EUR => self::AMOUNT_EUR,
        Rate::ORIG => self::AMOUNT_ORIG,
    ];

    public const string COMMENT_ADMIN_CASHBACK_BONUS = 'Cashback bonus';
    public const string COMMENT_ADMIN_CASHBACK_LOYALTY = 'Cashback loyalty';

    public const array GAME_GROUPS = [
        Game::TYPE_GROUP_CASINO => 'Casino',
        Game::TYPE_GROUP_BETTING => 'Betting',
        Game::TYPE_GROUP_MIXED => 'Mixed',
    ];

    public const string SHORT_TYPE_FIRST = 'F';
    public const string SHORT_TYPE_FIRST_TRY = 'FT';
    public const string SHORT_TYPE_REPEAT = 'R';
    public const string SHORT_TYPE_WITHDRAW = 'W';
    public const string SHORT_TYPE_BONUS = 'B';

    public const array SHORT_TYPES = [
        self::SHORT_TYPE_FIRST => 'First',
        self::SHORT_TYPE_FIRST_TRY => 'First Try',
        self::SHORT_TYPE_REPEAT => 'Repeat',
        self::SHORT_TYPE_WITHDRAW => 'Withdraw',
        self::SHORT_TYPE_BONUS => 'Bonus',
    ];

    public const int REWARD_PROVIDER_PRAGMATIC_PLAY = 1;
    public const int REWARD_PROVIDER_GAMZIX = 2;
    public const int REWARD_PROVIDER_SPINOMENAL = 3;
    public const int REWARD_PROVIDER_SPRIBE = 4;
    public const int REWARD_PROVIDER_EVOLUTION = 5;
    public const int REWARD_PROVIDER_EVOPLAY = 6;
    public const int REWARD_PROVIDER_QTECH = 7;
    public const int REWARD_PROVIDER_ENDORPHINA = 8;

    public const array REWARD_PROVIDERS = [
        self::REWARD_PROVIDER_PRAGMATIC_PLAY => 'pragmatic_play',
        self::REWARD_PROVIDER_GAMZIX => 'gamzix',
        self::REWARD_PROVIDER_SPINOMENAL => 'spinomenal',
        self::REWARD_PROVIDER_SPRIBE => 'spribe',
        self::REWARD_PROVIDER_EVOLUTION => 'evolution',
        self::REWARD_PROVIDER_EVOPLAY => 'evoplay',
        self::REWARD_PROVIDER_QTECH => 'qtech',
        self::REWARD_PROVIDER_ENDORPHINA => 'endorphina',
    ];

    public const array NOT_ENOUGH_MONEY_COMMENTS = [
        '301',
        'Немає достатньо грошей на рахунку',
        'недостатньо грошей на рахунку',
        'sur votre compte',
        'Non hai abbastanza denaro sul tuo conto',
        'No tienes suficiente dinero en tu cuenta',
        'Não tem dinheiro suficiente em sua conta',
        'Du har ikke nok penger på kontoen din',
        'Nie masz wystarczającej ilości pieniędzy na swoim koncie',
        'Saldo de conta insuficiente',
        'Saldo dell’account insufficiente',
        'The process has been aborted. You do not have sufficient funds in your account',
        'Yetersiz bakiye',
        'Недостаточно денег на счету',
        'Операция отменена. У вас недостаточно средств на счету',
    ];

    #[IntValidator]
    public int $site_id;
    #[StringValidator(1, 36)]
    public string $transaction_id;
    #[BigIdValidator]
    public int $user_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated_at;
    #[IntValidator]
    public int $op_id;
    #[BooleanValidator]
    public ?bool $is_first_success = false;
    #[IntValidator]
    public int $status = self::STATUS_SUCCESS;
    #[MoneyValidator(min: PHP_INT_MIN)]
    public ?string $amount_usd;
    #[MoneyValidator(min: PHP_INT_MIN)]
    public ?string $amount_rub;
    #[MoneyValidator(min: PHP_INT_MIN)]
    public ?string $amount_orig;
    #[CurrencyFormatValidator]
    public ?string $currency;
    #[IntValidator]
    public ?int $pay_sys_id;
    #[IntValidator]
    public ?int $ext_pay_sys_id;
    #[FilterValidator([self::class, 'cleanWallet'])]
    #[StringValidator(1, 100)]
    public ?string $wallet;
    #[MoneyValidator(min: -1000000)]
    public ?string $balance_after_changes;
    #[StringLimitedValidator(250)]
    public ?string $comment;
    #[StringLimitedValidator(250)]
    public ?string $comment_admin;
    #[MoneyValidator]
    public ?string $amount_locked;
    #[StringValidator]
    public ?string $comment_withdraw;
    #[FilterValidator([self::class, 'fillDirFromOpId'])]
    #[IntInArrayValidator(self::DIRS)]
    public ?int $dir;
    #[FilterValidator([self::class, 'fillBalanceTypeFromOpId'])]
    #[IntInArrayValidator(self::BALANCE_TYPES)]
    public ?int $balance_type;
    #[FilterValidator([self::class, 'fillExtTypeFromOpId'])]
    #[IntInArrayValidator(self::EXT_TYPES)]
    public ?int $ext_type;
    #[IntValidator]
    public ?int $from_wallet_id;
    #[IntValidator]
    public ?int $to_wallet_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $comment_admin_updated_at;
    #[StringValidator(1, 100)]
    public ?string $bonus_id;
    #[MoneyValidator(min: PHP_INT_MIN)]
    public ?string $amount_eur;
    #[StringValidator(1, 100)]
    public ?string $approved_by;
    #[IntValidator]
    public ?int $useragent_id;
    #[IntValidator]
    public ?int $refcode_id;
    #[IpValidator]
    public ?string $ip;
    #[StringValidator(1, 50)]
    public ?string $login_id;
    #[IntValidator]
    public ?int $requisite_id;
    #[StringValidator(2, 2)]
    public ?string $country;
    #[StringValidator(1, 32)]
    public ?string $auth_token;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $withdraw_approved_at;
    #[BooleanValidator]
    public ?bool $is_first_try = false;
    #[IntValidator]
    public ?int $host_id;
    #[StringValidator(1, 36)]
    public ?string $remote_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $approved_at;
    #[IntInArrayValidator(self::EXT_TYPES)]
    public ?int $game_group;
    #[DateTimeImmutableValidator(true)]
    public ?DateTimeImmutableWithMicroseconds $upserted_at;
    #[MoneyValidator]
    public ?string $fee_profit;
    #[MoneyValidator]
    public ?string $fee_wager;
    #[UuidValidator]
    public ?string $bonus_program_id;
    #[IntInArrayValidator(self::REWARD_PROVIDERS)]
    public ?int $reward_provider;
    #[IntValidator]
    public ?int $dep_number;

    public static function cleanWallet(mixed $value)
    {
        if ($value !== null) {
            $value = Str::cleanString(mb_substr(trim((string)$value), 0, 99));
        }

        return $value;
    }

    public static function getStatusById(?int $id): ?string
    {
        return static::STATUSES[$id] ?? null;
    }

    public static function getOperationById(?int $id): ?string
    {
        return static::OPERATIONS[$id] ?? (string) $id;
    }

    public static function getExtTypeById(?int $id): ?string
    {
        return static::EXT_TYPES[$id] ?? null;
    }

    public static function getBalanceTypeById(?int $id): ?string
    {
        return static::BALANCE_TYPES[$id] ?? null;
    }

    public static function getDirById(?int $id): ?string
    {
        return static::DIRS[$id] ?? null;
    }

    public static function getGameGroupById(?int $id): ?string
    {
        return static::GAME_GROUPS[$id] ?? null;
    }

    public static function getDateTypeItems(): array
    {
        return [
            'created_at' => 'Created',
            'updated_at' => 'Updated',
        ];
    }

    public static function fillDirFromOpId(mixed $value, UserTransactions $repo, array $row): int
    {
        return $value ?? PaymentTypeResolver::getDir((int)$row['op_id'], (float)$row['amount_orig']);
    }

    public static function fillBalanceTypeFromOpId(mixed $value, UserTransactions $repo, array $row): int
    {
        return $value ?? PaymentTypeResolver::getBalanceType((int)$row['op_id']);
    }

    public static function fillExtTypeFromOpId(mixed $value, UserTransactions $repo, array $row): int
    {
        return $value ?? PaymentTypeResolver::getExtType((int)$row['op_id']);
    }

    public static function isManualSuccessEligible(int $siteId, ?int $status, ?int $opId): bool
    {
        return in_array($siteId, Site::PLATFORM_SITES_SMEN, true)
            && self::STATUS_NEW === $status
            && self::OP_IN === $opId;
    }
}
