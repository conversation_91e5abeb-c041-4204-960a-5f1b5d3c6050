<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringValidator;

class CrmBulk extends BaseEntity
{
    #[IdValidator]
    public int $id;
    #[IdValidator]
    public int $site_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[IdValidator]
    public ?int $segment_id;
    #[IdValidator]
    public ?int $rule_id;
    #[IdValidator]
    public ?int $campaign_block_id;
    #[IdValidator]
    public ?int $template_id;
    #[IdValidator]
    public ?int $subject_id;
    #[StringValidator]
    public ?string $operator;
    #[StringValidator(0, 500)]
    public ?string $comment;
    #[StringValidator(1, 1)]
    public ?string $split_variant;
}
