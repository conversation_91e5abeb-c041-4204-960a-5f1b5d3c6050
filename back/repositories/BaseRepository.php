<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\exceptions\InvalidException;
use app\back\components\helpers\Json;
use app\back\components\validators\BaseValidator;
use app\back\entities\BaseEntity;
use Yiisoft\Db\Connection\ConnectionInterface;

abstract class BaseRepository
{
    use <PERSON>bFindHelper;
    use DbModifyHelper;

    // Must be override in children
    public const string ENTITY_CLASS = 'UndefinedModel::class';
    public const string TABLE_NAME = 'undefined_table';
    public const array PRIMARY_KEY = ['undefined_column'];

    final public function __construct(public ConnectionInterface $db)
    {
    }

    public function validateAndCreate(array $params): BaseEntity
    {
        /** @var BaseEntity $entityClass */
        $entityClass = static::ENTITY_CLASS;

        return new $entityClass($entityClass::importValidators()($params, $this));
    }

    public function validatePropOrException(BaseEntity $entity, string $propName, mixed $value, array $context = []): mixed
    {
        $prop = new \ReflectionProperty($entity, $propName);
        $attributes = $prop->getAttributes(BaseValidator::class, \ReflectionAttribute::IS_INSTANCEOF);

        if (empty($attributes)) {
            throw new \LogicException("Property has no validate attributes");
        }

        $value = $value ?? $prop->getValue($entity);
        $context = array_merge(get_object_vars($entity), $context);

        $type = $prop->getType();
        $allowsNull = $type === null || $type->allowsNull();

        foreach ($attributes as $attribute) {
            /** @var BaseValidator $validator */
            $validator = $attribute->newInstance();
            $value = $validator->prepare($value, $this, $context);

            if ($value === null) {
                if (!$allowsNull) {
                    throw new InvalidException("$propName is required");
                }

                if (!$validator::VALIDATE_ON_NULL) {
                    continue;
                }
            }

            $invalidMessage = $validator->validate($value, $this, $context);
            if ($invalidMessage !== null) {
                $value = is_scalar($value) ? $value : Json::encode($value);
                throw new InvalidException("$propName $invalidMessage ($value)");
            }

            $value = $validator->cast($value, $this);
        }

        return $value;
    }
}
