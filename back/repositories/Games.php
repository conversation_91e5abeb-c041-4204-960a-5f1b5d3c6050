<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\exceptions\ExpectationFailedException;
use app\back\entities\Game;

class Games extends BaseRepository
{
    use DbDictionaryHelper {
        getIdByName as innerGetIdByName;
    }

    public const string ENTITY_CLASS = Game::class;
    public const string TABLE_NAME = 'games';
    public const array PRIMARY_KEY = ['id'];
    public const string NAME_KEY = 'name';

    public function getIdByName(?string $name, array $fields = []): null|int|string
    {
        if ($name !== Game::SND_FAKE_GAME && $name !== Game::BETTING_NAME) {
            throw new \RuntimeException("It is danger to create new game, because of no unique key");
        }
        return $this->innerGetIdByName($name, $fields);
    }

    public function getBettingGameId(): int
    {
        return $this->getIdByName(Game::BETTING_NAME, ['platform' => Game::PLATFORM_WEB]);
    }

    public function getUniqNamesByPrefix(string $param): array
    {
        $map = $this->getNamesByPrefix($param);
        $map = array_unique($map);
        natcasesort($map);

        return array_combine($map, $map);
    }

    public function getBettingGameIds(): array
    {
        $result = array_keys(array_intersect($this->getNames(), Game::BETTING_GAME_NAMES));
        if (count($result) === 0) {
            throw new ExpectationFailedException("No betting games found. You need to update games sources");
        }

        return $result;
    }

    public function getIdsByNames(string ...$names): array
    {
        return array_keys(array_intersect($this->getNames(), $names));
    }
}
