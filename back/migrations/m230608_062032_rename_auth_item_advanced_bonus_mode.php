<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Connection\ConnectionInterface;

class m230608_062032_rename_auth_item_advanced_bonus_mode
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        $this->db->createCommand("UPDATE auth_assignment SET item_name = 'advanced-bonus-mode' WHERE item_name = 'assign-bonuses-full';")->execute();
        $this->db->createCommand("UPDATE auth_assignment_log SET permission = 'advanced-bonus-mode' WHERE permission = 'assign-bonuses-full';")->execute();
    }

    public function down(): void
    {
        $this->db->createCommand("UPDATE auth_assignment SET item_name = 'assign-bonuses-full' WHERE item_name = 'advanced-bonus-mode';")->execute();
        $this->db->createCommand("UPDATE auth_assignment_log SET permission = 'assign-bonuses-full' WHERE permission = 'advanced-bonus-mode';")->execute();
    }
}
