<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240422_133344_vb24_rename extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand()->update('sites', [
            'site_name' => 'VulkanStern',
            'short_name' => 'VSTRN',
            'domain' => 'vulkanstern.com',
        ], [
            'site_id' => 86,
        ])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->update('sites', [
            'site_name' => 'VulkanBet24',
            'short_name' => 'VB24',
            'domain' => 'vulkan24.bet',
        ], [
            'site_id' => 86,
        ])->execute();
    }
}
