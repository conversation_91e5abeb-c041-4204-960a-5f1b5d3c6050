<?php

declare(strict_types=1);

namespace app\back\migrations;

class m230710_082536_add_rank_column_to_s2p_users_table extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql('ALTER TABLE s2p_users ADD COLUMN rank smallint');
        $this->sql('ALTER TABLE s2p_users ADD COLUMN rank_updated_at timestamp(0)');
    }

    public function safeDown(): void
    {
        $this->sql('ALTER TABLE s2p_users DROP COLUMN rank');
        $this->sql('ALTER TABLE s2p_users DROP COLUMN rank_updated_at');
    }
}
