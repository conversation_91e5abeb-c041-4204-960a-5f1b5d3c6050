<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240513_121709_users_balances_daily_ggbetua_add_uah_balances_from_usd extends BaseMigration
{
    public function safeUp(): void
    {
        $sql = <<<SQL
WITH daily_uah_rate AS (
    SELECT
        r.date::date,
        (array_agg(r.rate ORDER BY r.date DESC))[1] AS rate
    FROM
        rates r
    WHERE
        code = 'UAH'
    GROUP BY
        date::date 
)
INSERT INTO users_balances_daily (site_id, date, currency, balance, "group", type)
SELECT
    b.site_id,
    b.date,
    'UAH' AS currency,
    ROUND(b.balance * daily_uah_rate.rate, 2) AS balance,
    b."group",
    b.type
FROM
    users_balances_daily b
INNER JOIN
    daily_uah_rate ON b.date::date = daily_uah_rate.date
WHERE
    b.currency = 'USD'
    AND b.site_id = 65
ON CONFLICT (site_id, date, currency, "group", type) DO NOTHING;
SQL;
        $this->sql($sql);
    }

    public function safeDown(): void
    {
        // since 2024-05-09 daily balances in UAH counted by task
        $this->sql("DELETE FROM users_balances_daily WHERE site_id = 65 AND currency = 'UAH' AND date < '2024-05-09'");
    }
}
