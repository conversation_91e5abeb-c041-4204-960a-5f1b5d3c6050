<?php

declare(strict_types=1);

namespace app\back\migrations;

class m241125_110800_wp_programs_traffic_types_tabel extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand()
            ->createTable('wp_programs_traffic_types', [
                'id' => 'integer generated by default as identity primary key',
                'name' => 'varchar(50) not null',
                'created_at' => 'timestamp(0) default now() not null',
            ])
            ->execute();

        $this->db->createCommand()
            ->addUnique('wp_programs_traffic_types', 'wp_programs_traffic_types_name', [
                'name'
            ])
            ->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropTable('wp_programs_traffic_types')->execute();
    }
}
