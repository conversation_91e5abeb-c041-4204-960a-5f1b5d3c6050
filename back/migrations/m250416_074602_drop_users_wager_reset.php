<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250416_074602_drop_users_wager_reset extends BaseMigration
{
    private const string TABLE_NAME = 'users_wager_reset';

    public function up(): void
    {
        $this->db->createCommand()->dropTable(self::TABLE_NAME)->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->createTable(self::TABLE_NAME, [
            'site_id' => 'integer not null',
            'bet_round_id' => 'varchar(90)',
            'user_id' => 'bigint not null',
            'wager_sum_on_bet_round_start' => 'numeric(20, 2)',
            'bet_sum_on_bet_round_start' => 'numeric(20, 2)',
            'current_game_action_wager_sum' => 'numeric(20, 2)',
            'current_game_action_bet_sum' => 'numeric(20, 2)',
            'created_at' => 'timestamp(0) not null',
        ])->execute();

        $this->db->createCommand()->addPrimaryKey(
            self::TABLE_NAME,
            self::TABLE_NAME . '_pk',
            ['site_id', 'user_id', 'created_at']
        )->execute();
    }
}
