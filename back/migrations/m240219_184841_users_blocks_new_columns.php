<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240219_184841_users_blocks_new_columns extends BaseMigration
{
    public function up(): void
    {
        $this->sql("ALTER TABLE users_blocks ADD COLUMN source smallint NOT NULL DEFAULT 1");
        $this->sql("ALTER TABLE users_blocks ADD COLUMN updated_by_user_id integer");
    }

    public function down(): void
    {
        $this->sql("ALTER TABLE users_blocks DROP COLUMN source");
        $this->sql("ALTER TABLE users_blocks DROP COLUMN updated_by_user_id");
    }
}
