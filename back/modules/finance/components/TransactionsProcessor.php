<?php

declare(strict_types=1);

namespace app\back\modules\finance\components;

use app\back\components\exceptions\ExpectationFailedException;
use app\back\entities\UserTransaction;
use app\back\entities\Withdrawal;
use app\back\modules\finance\withdrawals\WithdrawalsExtraInfo;
use app\back\modules\task\components\WithdrawalsLogger;
use app\back\repositories\UserTransactions;
use app\back\repositories\Withdrawals;

readonly class TransactionsProcessor
{
    public function __construct(
        private UserTransactions $userTransactionsRepo,
        private Withdrawals $withdrawalsRepo,
        private WithdrawalsLogger $log,
    ) {
    }

    public function processWithdrawal(int $siteId, string $transactionId, int $operatorId, int $decision, ?int $reason = null, ?WithdrawalsExtraInfo $info = null): void
    {
        $stats = $this->checkedUserTransactionForWithdrawal($siteId, $transactionId);

        if ($this->isTransactionAlreadyExists($siteId, $transactionId)) {
            return;
        }

        $withdrawal = new Withdrawal([
            'site_id' => $siteId,
            'transaction_id' => $transactionId,
            'user_id' => $stats->user_id,
            'status' => Withdrawal::STATUS_NEW,
            'operator_id' => $operatorId,
            'decision' => $decision,
            'reason' => $reason,
            'extra' => $info?->toArray(),
        ]);

        $this->withdrawalsRepo->insert($withdrawal);
    }

    public function processDeposit(Withdrawal $deposit): void
    {
        $this->checkedUserStatForDeposit($deposit->site_id, $deposit->transaction_id);

        if ($this->isTransactionAlreadyExists($deposit->site_id, $deposit->transaction_id)) {
            return;
        }

        $this->withdrawalsRepo->insert($deposit);
    }

    public function checkedUserTransactionForWithdrawal(int $siteId, $transactionId): UserTransaction
    {
        $callback = fn(UserTransaction $stat) => ($stat->ext_type === UserTransaction::EXT_TYPE_NORMAL && $stat->dir === UserTransaction::DIR_OUT);
        return $this->getEligibleUserStat($siteId, $transactionId, $callback);
    }

    public function checkedUserStatForDeposit(int $siteId, $transactionId): UserTransaction
    {
        $callback = fn(UserTransaction $us) => UserTransaction::isManualSuccessEligible($us->site_id, $us->status, $us->op_id);
        return $this->getEligibleUserStat($siteId, $transactionId, $callback);
    }

    private function getEligibleUserStat(int $siteId, $transactionId, callable $statCondition): UserTransaction
    {
        /** @var UserTransaction $transaction */
        $transaction = $this->userTransactionsRepo->findOne(['site_id' => $siteId, 'transaction_id' => $transactionId]);

        if ($transaction === null) {
            throw new ExpectationFailedException("Transaction {$siteId}-{$transactionId} not found");
        }

        if ($transaction->status !== UserTransaction::STATUS_NEW) {
            throw new ExpectationFailedException("Transaction {$siteId}-{$transactionId} is not new");
        }

        if (!$statCondition($transaction)) {
            throw new ExpectationFailedException("Transaction {$siteId}-{$transactionId} with invalid type or dir");
        }

        return $transaction;
    }

    private function isTransactionAlreadyExists(int $siteId, string $transactionId): bool
    {
        /** @var Withdrawal | null $withdrawal */
        $withdrawal = $this->withdrawalsRepo->findOne([
            'site_id' => $siteId,
            'transaction_id' => $transactionId,
            'status' => [Withdrawal::STATUS_NEW, Withdrawal::STATUS_SYNCED, Withdrawal::STATUS_PLANNED],
        ]);

        if ($withdrawal === null) {
            return false;
        }

        // Transaction with not synced withdrawal will be added for sync again
        // Assume that transaction with existing withdrawal is processed successfully
        if ($withdrawal->status === Withdrawal::STATUS_PLANNED) {
            throw new ExpectationFailedException('Transaction already planned. Remove from plan first');
        }
        $this->log->warning("Transaction is already processed ($siteId, $transactionId)");
        return true;
    }
}
