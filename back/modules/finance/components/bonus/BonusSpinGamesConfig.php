<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus;

class BonusSpinGamesConfig
{
    private array $config;

    public function __construct(
        array $remoteConfig,
        array $gamesIds,
    ) {
        $this->config = array_intersect_key($remoteConfig, array_flip(array_column($gamesIds, 'id')));
    }

    public function availableBetsInMoney(array $gameIds, ?string $currency): array
    {
        if ($currency === null) {
            return [];
        }

        $lists = [];
        foreach ($gameIds as $gameId) {
            $lists[] = ($this->config[$gameId]['freeSpinsBetsInMoneyMultiCurrency'][$currency]) ?? [];
        }
        return (count($lists) > 0) ? array_intersect(...$lists) : [];
    }

    public function availableCounts(array $gameIds): array
    {
        $lists = [];
        foreach ($gameIds as $gameId) {
            $lists[] = array_combine($this->config[$gameId]['freeSpinsCount'], $this->config[$gameId]['freeSpinsCount']);
        }
        return (count($lists) > 0) ? array_intersect(...$lists) : [];
    }

    public function availableLines(array $gameIds): array
    {
        $lists = [];
        foreach ($gameIds as $gameId) {
            $lists[] = $this->config[$gameId]['freeSpinsLines'] ?? [];
        }
        return (count($lists) > 0) ? array_intersect(...$lists) : [];
    }
}
