<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawalsRestrictions;

use app\back\components\AllowedLists;
use app\back\components\Form;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\WithdrawalRestriction;
use app\back\repositories\WithdrawalRestrictions;

class WithdrawalsRestrictionForm
{
    use Form;

    #[IntValidator(1, PHP_INT_MAX)]
    public ?int $dailySum;
    #[AllowedSiteValidator]
    public int $siteId;

    public function __construct(
        private readonly WithdrawalRestrictions $withdrawalRestrictions,
        public readonly AllowedLists $allowedLists,
    ) {
    }

    public function update(): void
    {
        /** @var WithdrawalRestriction $entity */
        $entity = $this->withdrawalRestrictions->findOne(['site_id' => $this->siteId]);

        if ($entity === null) {
            $entity = new WithdrawalRestriction();
            $entity->site_id = $this->siteId;
            $entity->daily_sum = $this->dailySum;
            $entity->updated_at = new \DateTimeImmutable(WithdrawalRestriction::SQL_NOW_DATETIME);
            $this->withdrawalRestrictions->insert($entity);
        } elseif (empty($this->dailySum)) {
            $this->withdrawalRestrictions->delete($entity);
        } else {
            $entity->daily_sum = $this->dailySum;
            $entity->updated_at = new \DateTimeImmutable(WithdrawalRestriction::SQL_NOW_DATETIME);
            $this->withdrawalRestrictions->update($entity, ['daily_sum', 'updated_at']);
        }
    }
}
