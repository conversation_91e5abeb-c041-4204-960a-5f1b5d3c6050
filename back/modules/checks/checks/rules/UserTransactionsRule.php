<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\RequiredWhenAnyNotEmptyValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\S2pOrder;
use app\back\repositories\S2pOrders;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UserTransactionsRule extends BaseSiteUserRule
{
    public const string AGGREGATOR_SUM = 'SUM';
    public const string AGGREGATOR_COUNT = 'COUNT';
    public const string AGGREGATOR_REQ = 'REQUISITES';

    public const array AGGREGATES = [
        self::AGGREGATOR_SUM => 'Sum',
        self::AGGREGATOR_COUNT => 'Count',
        self::AGGREGATOR_REQ => 'Uniq requisites',
    ];

    #[IntArrayValidator(S2pOrder::STATUSES)]
    public array $status = [];
    #[StringArrayValidator(S2pOrder::TYPES)]
    public array $direction = [];
    #[IntArrayValidator(S2pOrder::REQUISITE_TYPES)]
    public array $requisiteType = [];
    #[StringInArrayValidator(self::UNITS)]
    #[RequiredWhenAnyNotEmptyValidator('period')]
    public ?string $periodUnit = self::UNIT_HOUR;
    #[IntValidator]
    public ?int $period = null;
    #[StringInArrayValidator(self::OPERATORS)]
    public string $valueOperator = self::OPERATOR_GREATER;
    #[StringInArrayValidator(self::AGGREGATES)]
    public string $aggregator;
    #[IntValidator]
    public int $value;

    public static function getName(): string
    {
        return 'User Transactions (s2p)';
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectCell(3, 'status', 'Status', [
                    'list' => Arr::assocToIdName(S2pOrder::STATUSES),
                ]),
                $this->selectCell(2, 'direction', 'Direction', [
                    'list' => Arr::assocToIdName(S2pOrder::TYPES),
                ]),
                $this->selectCell(3, 'requisiteType', 'Requisite Type', [
                    'list' => Arr::assocToIdName(S2pOrder::REQUISITE_TYPES),
                ]),
                $this->radioListCell(4, 'aggregator', 'Aggregator', [
                    'list' => Arr::assocToIdName(self::AGGREGATES),
                ]),
            ],
            [
                $this->textInputCell(2, 'period', 'Period', [
                    'operators' => Arr::assocToIdName(self::UNITS),
                    'operatorPostfix' => 'Unit',
                ]),
                $this->textInputCell(2, 'value', 'Value', [
                    'hint' => 'For Sum in USD',
                    'operators' => Arr::assocToIdName(self::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
        ];
    }

    public function getParamsSignatureParts(): array
    {
        return ['periodUnit', 'period', 'status', 'direction', 'requisiteType', 'aggregator', 'value', 'valueOperator'];
    }

    public function getLabel(): string
    {
        $statuses = [];
        foreach ($this->status as $status) {
            $statuses[] = S2pOrder::getStatusById((int) $status);
        }
        $statuses = implode(', ', $statuses);

        $directions = [];
        foreach ($this->direction as $direction) {
            $directions[] = S2pOrder::getTypeById($direction);
        }
        $directions = implode(', ', $directions);

        $requisiteTypes = [];
        foreach ($this->requisiteType as $requisiteType) {
            $requisiteTypes[] = S2pOrder::getRequisiteTypeById((int) $requisiteType);
        }
        $requisiteTypes = implode(', ', $requisiteTypes);

        $period = 'LT';
        if (!empty($this->period)) {
            $period = $this->period . " " . $this->periodUnit;
        }

        $aggregator = (self::AGGREGATES)[$this->aggregator];

        return "<span class=\"badge bg-secondary\">$requisiteTypes $statuses $directions {$aggregator} (for $period) {$this->valueOperator} {$this->value}</span>";
    }

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $aggFunction = match ($this->aggregator) {
            static::AGGREGATOR_SUM => 'COALESCE(SUM(o.summ), 0)',
            static::AGGREGATOR_COUNT => 'COALESCE(COUNT(o.id), 0)',
            static::AGGREGATOR_REQ => 'COALESCE(COUNT(DISTINCT o.requisite), 0)',
            default => throw new \Exception('Invalid aggregator'),
        };

        $inBuilder->setMap([
            "u.site_id" => 'intval',
            "u.user_id" => 'intval',
        ]);

        $conditions = [
            'AND',
            'o.site_id = u.site_id',
            'o.user_id = u.user_id',
        ];

        if (!empty($this->status)) {
            $conditions[] = ['o.status' => $this->status];
        }

        if (!empty($this->direction)) {
            $conditions[] = ['o.type' => $this->direction];
        }

        if (!empty($this->requisiteType)) {
            $conditions[] = ['o.requisite_type' => $this->requisiteType];
        }

        if (!empty($this->period)) {
            $conditions[] = "o.date > NOW() - INTERVAL '{$this->period} {$this->periodUnit}'";
        }

        $query = (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => "({$aggFunction} {$this->valueOperator} {$this->value})::bool",
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('u'),
            ])
            ->from($inBuilder->table('u', ['site_id', 'user_id']))
            ->leftJoin(['o' => S2pOrders::TABLE_NAME], $conditions)
            ->groupBy([
                'u.site_id',
                'u.user_id',
            ]);

        return $query;
    }
}
