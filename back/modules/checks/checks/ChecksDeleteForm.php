<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks;

use app\back\components\Form;
use app\back\components\validators\IdValidator;
use app\back\repositories\Checks;

class ChecksDeleteForm
{
    use Form;

    #[IdValidator]
    public int $id;

    public function __construct(
        private readonly Checks $checksRepo,
    ) {
    }

    public function delete(): void
    {
        $entity = $this->checksRepo->findOneOr404(['id' => $this->id]);
        $this->checksRepo->delete($entity);
    }
}
