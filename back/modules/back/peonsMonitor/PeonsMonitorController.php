<?php

declare(strict_types=1);

namespace app\back\modules\back\peonsMonitor;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;
use app\back\modules\task\components\PeonsMaster;

#[AccessCheckPage]
class PeonsMonitorController extends WebController
{
    public function actionInfo(PeonsMonitorInfoForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionNotifyQueue(PeonsMonitorNotifyForm $form, SessionMessages $messages, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->notify();
    }

    public function actionResetQueue(PeonsMonitorResetForm $form, SessionMessages $messages, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->reset();
    }

    public function actionStopAll(PeonsMaster $peonsMaster, SessionMessages $messages): void
    {
        $peonsMaster->stopAll();
        $messages->success("Stop all sent");
    }

    public function actionResetAllStatistic(PeonsMaster $peonsMaster, SessionMessages $messages): void
    {
        $peonsMaster->deleteAllStatisticKeys();
        $messages->success("Reset all statistic is sent");
    }
}
