<?php

declare(strict_types=1);

namespace app\back\modules\back\s2pProjectsFlags;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class S2pProjectsFlagsController extends WebController
{
    public function actionData(S2pProjectFlagsFilterForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionUpdate(S2pProjectsFlagsEditForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $this->bl()->modify($request->json());
    }
}
