<?php

declare(strict_types=1);

namespace app\back\modules\back\employees;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\BaseAuthAccess;
use app\back\components\Request;
use app\back\components\ResponseCsv;
use app\back\components\SessionMessages;
use app\back\components\WebController;

#[AccessCheckPage]
class EmployeesController extends WebController
{
    public function actionList(EmployeesForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionDownload(EmployeesForm $form, Request $request, BaseAuthAccess $auth): ResponseCsv
    {
        $form->validateOrException($request->json());

        return new ResponseCsv($form->emails(), separator: $auth->employee()->getSettingsObject()->getSeparator());
    }

    public function actionCreateForm(EmployeeCreateForm $form): array
    {
        return $form->response();
    }

    public function actionCreate(EmployeeCreateForm $form, SessionMessages $messages, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->create();
        $this->bl()->create($request->json());
        $messages->success('Created successfully');
    }

    public function actionUpdateForm(EmployeeUpdateForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        $form->fillFromEmployee();

        return $form->response();
    }

    public function actionUpdate(EmployeeUpdateForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $changes = $form->process();

        if (!empty($changes)) {
            $changes['employee_id'] = $form->employeeId;
            $this->bl()->modify($changes);
            $messages->success('User data has been updated.');
        }
    }

    public function actionLogin(EmployeeLoginForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $employee = $form->login();
        $messages->success("Logged in as {$employee->email}");
    }

    public function actionPermissions(EmployeePermissionsForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->response();
    }

    public function actionPermissionSave(EmployeePermissionUpdateForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->save();

        $this->bl()->modify($request->json());
    }

    public function actionCreateRoleForm(CreateRoleFromEmployeePermissionsForm $form): array
    {
        return $form->response();
    }

    public function actionCreateRole(CreateRoleFromEmployeePermissionsForm $form, Request $request, SessionMessages $sessionMessages): void
    {
        $json = $request->json();
        $form->validateOrException($json);
        $form->create();
        $this->bl()->modify($json);
        $sessionMessages->success('New role is created and assigned');
    }

    public function actionRefreshPgp(EmployeePgpKeyRefreshForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $changes = $form->refresh();
        $this->bl()->modify([...$request->json(), ...$changes]);
    }
}
