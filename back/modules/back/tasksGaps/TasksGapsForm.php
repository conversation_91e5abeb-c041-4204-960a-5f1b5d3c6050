<?php

declare(strict_types=1);

namespace app\back\modules\back\tasksGaps;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\components\RichTable;
use app\back\components\validators\BooleanArrayValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\config\tasks\Res;
use app\back\repositories\Tasks;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class TasksGapsForm
{
    use RichTable;

    #[StringMultilineValidator]
    public ?string $name = null;
    #[StringArrayValidator]
    public array $resource = [];
    #[DateValidator]
    public string $from;
    #[DateValidator]
    public ?string $to = null;
    #[BooleanArrayValidator]
    public array $error = [];
    #[BooleanArrayValidator]
    public array $ended = [];

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
        $this->from = date('Y-m-d');
    }

    protected function columns(array $context): array
    {
        return [
            //['name' => 'Name', 'code' => 'name'],
            //['name' => 'Resource', 'code' => 'resource'],
            //['name' => 'From', 'code' => 'from'],
            ['name' => 'Steps', 'code' => 'steps'],
            ['name' => 'Command', 'code' => 'command', 'align' => 'start'],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectBooleanCell(1, 'error', 'Error'),
                $this->selectBooleanCell(1, 'ended', 'Ended'),
                $this->dateCell(1, 'from', 'From'),
                $this->dateCell(1, 'to', 'To'),
                $this->textAreaLiveSearchCell(3, 'name', 'Task name', '/back/tasks-history/autocomplete-name', [
                    'focusOnMount' => true,
                ]),
                $this->selectCell(2, 'resource', 'Resource', [
                    'list' => Arr::columnToIdName(Res::all()),
                ]),
                $this->submitCell(1, 'Filter'),
            ],
        ];
    }

    protected function data(): array
    {
        $gaps = (new Query($this->db))
            ->select([
                'name',
                'resource',
                'period' => Db::intervalToIso8601('MIN(period)'),
                'range' => "UNNEST(tsmultirange(tsrange(MIN(t.from),MAX(t.from), '[)')) - range_agg(tsrange(t.from, t.from + t.period, '[)')))",
            ])
            ->from(['t' => Tasks::TABLE_NAME])
            ->where(['>=', 't.from', $this->from])
            ->andWhere(['>=', 't.created_at', $this->from]) // TODO: Only for fast index search. Remove after index in from column
            ->andFilterWhere(['t.name' => StringMultilineValidator::getAsArray($this->name)])
            ->andFilterWhere(['t.resource' => $this->resource])
            ->andFilterWhere(['t.error' => $this->error])
            ->andFilterWhere(['t.ended' => $this->ended])
            ->andFilterWhere(['<', 't.from', $this->to])
            ->groupBy(new Expression('1, 2'))
            ->orderBy(new Expression('1, 2, 3'));

        $query = (new Query($this->db))
            ->withQuery($gaps, 'gaps')
            ->select([
                'name',
                'resource',
                'from' => 'lower(range)',
                'to' => 'upper(range)',
                'steps' => '(EXTRACT(epoch FROM (upper(range) - lower(range))) / NULLIF(EXTRACT(epoch FROM period::interval), 0))::integer',
                'step' => 'period',
                'command' => "'./yii task/reload ' || name || ' ' || resource || ' \"' ||  lower(range) || '\" \"' || upper(range) || '\" ' || period",
            ])
            ->from('gaps');

        return $query->all();
    }
}
