<?php

declare(strict_types=1);

namespace app\back\modules\back\tasksGaps;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class TasksGapsController extends WebController
{
    public function actionData(TasksGapsForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionRestart(TasksGapsRestartForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->restart();
    }
}
