<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\gamesHhs;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\validators\DateValidator;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\HhsGames;
use app\back\repositories\HhsUserGameSessions;
use Yiisoft\Db\Query\Query;

class GamesHhsForm extends BasePlayerForm
{
    use RichTable;

    #[DateValidator]
    public ?string $from = null;
    #[DateValidator]
    public ?string $to = null;

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
        private readonly HhsGames $hhsGamesRepo,
    ) {
        parent::__construct($allowedLists, $db, $auth);
        $this->from = $this->getSettingFrom();
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Game', 'code' => 'game'],
            ['name' => 'Currency', 'code' => 'currency'],
            ['name' => 'Spins', 'code' => 'bet_count'],
            ['name' => 'Bet sum', 'code' => 'bet_sum'],
            ['name' => 'Win sum', 'code' => 'win_sum'],
            ['name' => 'Payout (%)', 'code' => 'payout'],
            ['name' => 'Profit', 'code' => 'profit'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateCell(2, 'from', 'From'),
                $this->dateCell(2, 'to', 'To', [
                    'buttonsMode' => 'end',
                    'placeholder' => 'now',
                ]),
            ],
        ];
    }

    public function data(): array
    {
        $query = (new Query($this->db))
            ->select([
                'game_id' => 'game_id',
                'currency' => 'currency',
                'bet_count' => 'SUM(bet_count)',
                'bet_sum' => "SUM(bet_sum + double_bet_sum)",
                'win_sum' => "SUM(win_sum + double_win_sum)",
                'payout' => "ROUND(SUM(win_sum + double_win_sum) / NULLIF(SUM(bet_sum + double_bet_sum), 0) * 100, 2)",
                'profit' => "SUM(bet_sum + double_bet_sum - (win_sum + double_win_sum))",
            ])
            ->from(HhsUserGameSessions::TABLE_NAME)
            ->groupBy(['game_id', 'currency'])
            ->orderBy(['profit' => SORT_ASC]);

        $data = $this->applyFilters($query)
            ->all();

        foreach ($data as &$row) {
            $row['game'] = $this->hhsGamesRepo->getNameById($row['game_id']);
        }

        return $data;
    }

    public function applyFilters(Query $query): Query
    {
        $to = $this->to ? DateHelper::nextDay($this->to) : null;

        $query->where($this->siteUser())
            ->andFilterWhere(['>=', 'updated_at', $this->from])
            ->andFilterWhere(['<', 'updated_at', $to]);

        return $query;
    }

    public function total(): int
    {
        $query = (new Query($this->db))
            ->select(['COUNT(DISTINCT game_id)'])
            ->from(HhsUserGameSessions::TABLE_NAME);
        $this->applyFilters($query);

        return $query->scalar();
    }
}
