<?php

declare(strict_types=1);

namespace app\back\modules\user\player;

use app\back\components\helpers\Str;
use app\back\components\Permission;
use app\back\modules\user\player\blocks\bets\BetsForm;
use app\back\modules\user\player\blocks\bonuses\BonusesForm;
use app\back\modules\user\player\blocks\bonusesActivation\BonusesActivationForm;
use app\back\modules\user\player\blocks\bonusOffers\BonusOffersForm;
use app\back\modules\user\player\blocks\chats\ChatsForm;
use app\back\modules\user\player\blocks\contacts\ContactsForm;
use app\back\modules\user\player\blocks\contactsHistory\ContactsHistoryForm;
use app\back\modules\user\player\blocks\crm\CrmForm;
use app\back\modules\user\player\blocks\customer\CustomerForm;
use app\back\modules\user\player\blocks\comments\CommentsForm;
use app\back\modules\user\player\blocks\games\GamesForm;
use app\back\modules\user\player\blocks\gamesHhs\GamesHhsForm;
use app\back\modules\user\player\blocks\history\HistoryForm;
use app\back\modules\user\player\blocks\kyc\KycForm;
use app\back\modules\user\player\blocks\logins\LoginsForm;
use app\back\modules\user\player\blocks\lootboxes\LootboxesForm;
use app\back\modules\user\player\blocks\payInfo\PayInfoForm;
use app\back\modules\user\player\blocks\referralReferences\ReferralsForm;
use app\back\modules\user\player\blocks\requisites\RequisitesForm;
use app\back\modules\user\player\blocks\tickets\TicketsFilterForm;
use app\back\modules\user\player\blocks\transactions\TransactionsForm;
use app\back\modules\user\player\blocks\wallets\WalletsForm;
use app\back\modules\user\player\blocks\wheelFortune\WheelFortuneForm;
use app\back\repositories\PermissionSecuredDict;

class PlayerBlocks implements PermissionSecuredDict
{
    public const array BLOCK_CLASS_MAP = [
        'bets' => BetsForm::class,
        'bonus-offers' => BonusOffersForm::class,
        'bonuses' => BonusesForm::class,
        'bonuses-activation' => BonusesActivationForm::class,
        'chats' => ChatsForm::class,
        'comments' => CommentsForm::class,
        'contacts' => ContactsForm::class,
        'contacts-history' => ContactsHistoryForm::class,
        'crm' => CrmForm::class,
        'customer' => CustomerForm::class,
        'games' => GamesForm::class,
        'games-hhs' => GamesHhsForm::class,
        'history' => HistoryForm::class,
        'kyc' => KycForm::class,
        'logins' => LoginsForm::class,
        'lootboxes' => LootboxesForm::class,
        'pay-info' => PayInfoForm::class,
        'referrals' => ReferralsForm::class,
        'requisites' => RequisitesForm::class,
        'tickets' => TicketsFilterForm::class,
        'transactions' => TransactionsForm::class,
        'wallets' => WalletsForm::class,
        'wheel-fortune' => WheelFortuneForm::class,
    ];

    public function permissionNames(): array
    {
        return array_map(static fn($c) => Str::className2Words($c, '', 'Form'), self::BLOCK_CLASS_MAP);
    }

    public function permissionPrefix(): string
    {
        return Permission::PERM_VIEW_PLAYER_PREFIX;
    }
}
