<?php

declare(strict_types=1);

namespace app\back\modules\user\chatsViewer;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class ChatsViewerController extends WebController
{
    public function actionForm(ChatsViewerForm $form): array
    {
        return $form->response();
    }

    public function actionLoad(ChatsViewerForm $form, Request $request): array
    {
        $result = $form->validateAndResponse($request->json());
        $result['chats'] = $form->data();

        return $result;
    }

    public function actionChat(ChatsViewerChatForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->getChat();
    }
}
