<?php

declare(strict_types=1);

namespace app\back\modules\user\manager\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Str;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BigIdMultilineValidator;
use app\back\modules\task\components\FetchTaskFactory;

class ManagerBonusBlackListRemoveForm
{
    use FormGrid;

    #[BigIdMultilineValidator]
    public string $userId;
    #[AllowedSiteValidator]
    public int $siteId;

    public function __construct(
        private readonly BaseAuthAccess $auth,
        public readonly AllowedLists $allowedLists,
        private readonly FetchTaskFactory $requestFactory,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectSiteCell(5, 'siteId', 'Site', ['multiple' => false]),
                $this->textAreaCell(5, 'userId', 'User ID'),
                $this->submitCell(2, 'Remove', ['buttonStyle' => 'btn-danger']),
            ],
        ];
    }

    public function process(): bool
    {
        $userIds = Str::explodeText($this->userId);
        $employee = $this->auth->employee();

        $fetchTask = $this->requestFactory->createFetchTask('users-remove-from-bonus-black-list', $this->siteId, [
            'params' => [
                'userIds' => $userIds,
                'operator' => $employee->email,
                'auto' => 0,
            ],
        ]);

        return $fetchTask->sendAndCheckSuccess();
    }
}
