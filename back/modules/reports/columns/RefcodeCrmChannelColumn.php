<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\Refcode;
use app\back\repositories\Refcodes;
use Yiisoft\Db\Connection\ConnectionInterface;

class RefcodeCrmChannelColumn extends BaseColumn implements Decorated, Filtered, Selected, Operators
{
    public string $column = 'code';

    public string $title = 'CRM channel';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(Refcode::CRM_CHANNELS),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, Refcode::CRM_CHANNELS];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return Refcodes::crmChannelsCase($this->tableAlias, $this->column);
    }

    public function decorate($value, array $row)
    {
        return Refcode::crmChannelById($value);
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }
}
