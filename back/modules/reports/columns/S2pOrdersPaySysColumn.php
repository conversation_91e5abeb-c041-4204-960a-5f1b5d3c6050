<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\validators\IntArrayValidator;
use app\back\repositories\S2pPaySystems;
use Yiisoft\Db\Connection\ConnectionInterface;

class S2pOrdersPaySysColumn extends BaseColumn implements Decorated, Selected, Filtered, Operators
{
    public string $column = 'pay_sys_id';
    public string $title = 'Pay system';

    public function __construct(
        private readonly S2pPaySystems $s2pPaySystemsRepo,
    ) {
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'allowLiveSearch' => true,
            'liveSearchUrl' => '/dictionaries/filtered/s2p-pay-systems',
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function decorate($value, array $row)
    {
        return $this->s2pPaySystemsRepo->getNameById($value);
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }
}
