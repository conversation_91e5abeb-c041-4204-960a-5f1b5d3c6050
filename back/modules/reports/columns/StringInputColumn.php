<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\validators\StringValidator;

class StringInputColumn extends BaseColumn implements Input
{
    public string $title = '';

    public function inputProps(): array
    {
        return [
            'type' => 'text-input',
        ];
    }

    public function rule(): array
    {
        return [StringValidator::class];
    }
}
