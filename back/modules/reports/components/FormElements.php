<?php

declare(strict_types=1);

namespace app\back\modules\reports\components;

use app\back\components\helpers\Arr;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Input;
use app\back\modules\reports\columns\OperatorComponent;
use app\back\modules\reports\columns\Operators;

trait FormElements
{
    public const string CATEGORY_FILTERS = 'filters';
    public const array FILTERS_CATEGORIES = [
        self::CATEGORY_FILTERS,
    ];

    private array $filterElements;

    abstract protected function elementsConfigs(string $category): array;

    public function configureElement(BaseColumn $element): void
    {
    }

    public function filterColumn(string $filterName): Input & BaseColumn
    {
        $this->filterElements ??= Arr::groupsToFlat(array_merge(...array_map(fn($c) => $this->elements($c), static::FILTERS_CATEGORIES)));

        if (!array_key_exists($filterName, $this->filterElements)) {
            throw new \LogicException("Filter $filterName is not a valid, looks like created dynamically");
        }

        return $this->filterElements[$filterName];
    }

    public function filtersNames(): array
    {
        return array_merge(...array_map(fn($c) => $this->elementsNames($c), static::FILTERS_CATEGORIES));
    }

    public function elementsNames(string $category): array
    {
        [$closure] = $this->elementsConfigs($category);
        return array_keys(Arr::groupsToFlat($closure()));
    }

    public function elements(string $category): array
    {
        [$closure, $instanceCheck] = $this->elementsConfigs($category);

        $blocks = [];
        foreach ($closure() as $blockName => $columns) {
            foreach ($columns as $colName => $colConfig) {
                /** @var BaseColumn $column */
                $column = $this->container->getNew(array_shift($colConfig));
                $column->configure($colConfig, $instanceCheck);
                $this->configureElement($column);
                $blocks[$blockName][$colName] = $column;
            }
        }
        return $blocks;
    }

    public function elementsToDropdown(string $category, array $options = []): array
    {
        $result = [];
        foreach ($this->elements($category) as $blockName => $block) {
            $blockColumns = [];

            foreach ($block as $columnName => $column) {
                /** @var BaseColumn $column */
                $blockColumns[$columnName] = $column->title;
            }
            $result[$blockName] = $blockColumns;
        }

        return array_merge(Arr::assocGroupsToIdName($result), $options);
    }

    public function elementsToInputs(string $category): array
    {
        $inputsSets = [];
        foreach ($this->elements($category) as $blockName => $columns) {
            $inputs = [];
            foreach ($columns as $name => $column) {
                /** @var BaseColumn|Input $column */

                $input = [
                    'name' => $name,
                    'title' => $column->title,
                    'props' => $column->inputProps(),
                    'width' => $column->width(),
                ];

                if ($column instanceof OperatorComponent) {
                    $input['operatorComponent'] = $column->operatorComponentProps();
                }

                if ($column instanceof Operators) {
                    $operatorNames = array_map(static fn($o) => Operators::NAMES[$o], $column->operators());
                    $input['operators'] = Arr::assocToIdName(array_combine($column->operators(), $operatorNames));
                }

                $inputs[] = $input;
            }
            usort($inputs, static fn ($c1, $c2) => $c1['title'] <=> $c2['title']);

            $inputsSets[] = [
                'title' => $blockName,
                'inputs' => $inputs
            ];
        }

        return $inputsSets;
    }
}
