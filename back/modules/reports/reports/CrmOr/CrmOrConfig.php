<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\CrmOr;

use app\back\components\helpers\DateHelper;
use app\back\entities\CrmLetter;
use app\back\entities\CrmRule;
use app\back\entities\UserContact;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CrmRuleTitleColumn;
use app\back\modules\reports\columns\CrmRuleTypeColumn;
use app\back\modules\reports\columns\CrmSegmentNameColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\PercentColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserContactServiceProviderColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\CrmBulks;
use app\back\repositories\CrmLetters;
use app\back\repositories\CrmRules;
use app\back\repositories\CrmSegments;
use app\back\repositories\UserContacts;
use app\back\repositories\Users;
use Yiisoft\Db\Query\Query;

class CrmOrConfig extends BaseReportConfig
{
    private string $sendExpression = 'COUNT(DISTINCT l.bulk_id::text || l.user_id::text)';
    private string $openExpression = 'COUNT(DISTINCT l.bulk_id::text || l.user_id::text) FILTER (WHERE l.opened_at IS NOT NULL)';
    private string $clickExpression = 'COUNT(DISTINCT l.bulk_id::text || l.user_id::text) FILTER (WHERE l.clicked_at IS NOT NULL)';

    public function rules(): array
    {
        return [
            [['site_id', 'rule_type', 'date'], 'required']
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), '>='],
            ['date', DateHelper::yesterday(), '<='],
            ['site_id', []],
            ['rule_type', [CrmRule::TYPE_ONCE]],
            ['groups', ['date']],
            ['metrics', ['send', 'open', 'click', 'send_to_open', 'open_to_click']],

        ];
    }

    public function beforeQuery(): void
    {
        $this->request->orders[$this->request->groups[0] ?? 'date'] = SORT_DESC;

        parent::beforeQuery();
    }

    public function filters(): array
    {
        return [
            'Main' => [
                'date' => [DateColumn::class, ['b' => 'created_at']],
                'site_id' => [SiteIdColumn::class, 'b'],
                'country' => [CountryColumn::class, 'u'],
                'rule_type' => [CrmRuleTypeColumn::class, 'r'],
                'rule' => [CrmRuleTitleColumn::class, 'r'],
                'segment' => [CrmSegmentNameColumn::class, 's'],
                'sp_id' => [UserContactServiceProviderColumn::class, 'uc'],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            'Main' => [],
        ];
    }

    public function metrics(): array
    {
        return [
            'Main' => [
                'send' => [CountColumn::class, ['expr' => $this->sendExpression, 'l'], 'title' => 'Send'],
                'open' => [CountColumn::class, ['expr' => $this->openExpression, 'l'], 'title' => 'Open'],
                'click' => [CountColumn::class, ['expr' => $this->clickExpression, 'l'], 'title' => 'Click'],
                'send_to_open' => [PercentColumn::class, ['expr' => "CASE WHEN ($this->sendExpression > 0) THEN ($this->openExpression::float / $this->sendExpression * 100) ELSE 0 END", 'l'], 'title' => 'Send to open'],
                'open_to_click' => [PercentColumn::class, ['expr' => "CASE WHEN ($this->openExpression > 0) THEN ($this->clickExpression::float / $this->openExpression * 100) ELSE 0 END", 'l'], 'title' => 'Open to click'],
            ],
        ];
    }

    public function groups(): array
    {
        return [
            'Main' => [
                'date' => [DayColumn::class, ['b' => 'created_at']],
                'week' => [WeekColumn::class, ['b' => 'created_at']],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'l' => [function (Query $query) {
                $query->andWhere(['AND',
                    ['l.status' => CrmLetter::STATUS_OK],
                ]);
                return CrmLetters::TABLE_NAME;
            }],
            'b' => [CrmBulks::TABLE_NAME, 'b.id = l.bulk_id AND b.site_id = l.site_id', ['l'], 'INNER JOIN'],
            'r' => [function (Query $query) {
                $query->andWhere(['AND',
                    ['r.provider_type' => CrmRule::PROVIDER_TYPE_EMAIL],
                ]);
                return CrmRules::TABLE_NAME;
            }, 'r.id = b.rule_id AND r.site_id = b.site_id', ['b'], 'INNER JOIN'],
            's' => [CrmSegments::TABLE_NAME, 's.id = b.segment_id', ['b'], 'INNER JOIN'],
            'u' => [Users::TABLE_NAME, 'u.site_id = l.site_id AND u.user_id = l.user_id', ['l'], 'INNER JOIN'],
            'uc' => [UserContacts::TABLE_NAME, 'uc.site_id = u.site_id AND uc.user_id = u.user_id AND uc.value = u.email AND uc.type = ' . UserContact::TYPE_EMAIL, ['u'], 'INNER JOIN']
        ];
    }
}
