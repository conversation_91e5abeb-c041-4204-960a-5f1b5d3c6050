<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\S2pTransactions;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\entities\Bin;
use app\back\entities\S2pTransaction;
use app\back\entities\UserIgnoreId;
use app\back\modules\reports\columns\BinBankColumn;
use app\back\modules\reports\columns\BinColumn;
use app\back\modules\reports\columns\BinCountryColumn;
use app\back\modules\reports\columns\BinCurrencyColumn;
use app\back\modules\reports\columns\BinGlobalPaysysColumn;
use app\back\modules\reports\columns\BinStatusColumn;
use app\back\modules\reports\columns\BinTypeColumn;
use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CountryRealColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DateTypeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\IpColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\RequisiteColumn;
use app\back\modules\reports\columns\S2pInstanceColumn;
use app\back\modules\reports\columns\S2pOrderInvoiceIdColumn;
use app\back\modules\reports\columns\S2pOrdersDeviceColumn;
use app\back\modules\reports\columns\S2pOrdersIdColumn;
use app\back\modules\reports\columns\S2pOrdersMidColumn;
use app\back\modules\reports\columns\S2pOrdersPayClassColumn;
use app\back\modules\reports\columns\S2pOrdersPaySourceColumn;
use app\back\modules\reports\columns\S2pOrdersPaySysColumn;
use app\back\modules\reports\columns\S2pOrdersPayTypeColumn;
use app\back\modules\reports\columns\S2pOrdersStatusColumn;
use app\back\modules\reports\columns\S2pOrdersStatusIdColumn;
use app\back\modules\reports\columns\S2pOrdersTrustLevelColumn;
use app\back\modules\reports\columns\S2pOrdersTrustScoreColumn;
use app\back\modules\reports\columns\S2pProjectIdColumn;
use app\back\modules\reports\columns\S2pTransactionDirColumn;
use app\back\modules\reports\columns\S2pTransactionIdColumn;
use app\back\modules\reports\columns\S2pTransactionStatusColumn;
use app\back\modules\reports\columns\S2pTransactionTypeColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UsersS2pTrustScore;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Bins;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\S2pTransactions;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\Users;

class S2pTransactionsConfig extends BaseReportConfig
{
    private const array DATE_TYPES = [
        DateTypeColumn::CREATED => S2pTransaction::COL_CREATED_AT,
        DateTypeColumn::UPDATED => S2pTransaction::COL_UPDATED_AT,
        DateTypeColumn::SUCCESS => S2pTransaction::COL_SUCCESS_AT,
    ];

    private string $transactionDateType = S2pTransaction::COL_SUCCESS_AT;
    private bool $canViewS2pPayClasses = true;
    private bool $canViewS2pTrustGroups = true;
    private bool $canViewS2pTrustScore = true;
    private bool $canViewS2pMids = true;

    public function init(): void
    {
        $authAccess = $this->container->get(BaseAuthAccess::class);
        $this->canViewS2pPayClasses = $authAccess->canViewS2pPayClasses();
        $this->canViewS2pTrustGroups = $authAccess->canViewS2pTrustGroups();
        $this->canViewS2pTrustScore = $authAccess->canViewS2pTrustScore();
        $this->canViewS2pMids = $authAccess->canViewS2pMids();
    }

    public function rules(): array
    {
        return [
            [['date', 'date_type',], 'required'],
            [['site_id', 'project_id'], 'required', 'message' => 'Please specify Site or Project', 'when' => function () {
                return $this->request->allEmptyFilter('site_id', 'project_id');
            }],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), Operators::GE],
            ['date', DateHelper::yesterday(), Operators::LE],
            ['project_id', []],
            ['date_type', DateTypeColumn::SUCCESS],
            ['status', [S2pTransaction::STATUS_SUCCESS]],
            ['dir', [S2pTransaction::DIR_IN]],
            ['columns', ['transaction_id', 'order_id', 'date_created', 'date_updated', 'date_success', 'project_id', 'user_id', 'type', 'dir', 'status', 'pay_sys_id', 'amount_usd', 'amount_eur']],
            ['ignore', UserIgnoreId::MODE_IGNORE],
        ];
    }

    protected function beforeQuery(): void
    {
        $this->transactionDateType = self::DATE_TYPES[$this->request->getFilter('date_type') ?? DateTypeColumn::SUCCESS];
    }

    public function filters(): array
    {
        return [
            'Transactions' => [
                'date' => [DateColumn::class, ['t' => $this->transactionDateType]],
                'date_type' => [DateTypeColumn::class, 't', 'types' => array_keys(self::DATE_TYPES)],
                'project_id' => [S2pProjectIdColumn::class, 't'],
                'type' => [S2pTransactionTypeColumn::class, 't'],
                'dir' => [S2pTransactionDirColumn::class, 't'],
                'status' => [S2pTransactionStatusColumn::class, 't'],
                'status_id' => [S2pOrdersStatusIdColumn::class, 't', 'title' => 'Status id'],
                'site_id' => [SiteIdColumn::class, 'o'],
                'user_id' => [UserIdColumn::class, 'o'],
                'user_сid' => [UserCidColumn::class, 'u'],
                'transaction_id' => [S2pTransactionIdColumn::class, 't'],
                'order_id' => [S2pOrdersIdColumn::class, ['t' => 'order_id'], 'decorateS2pLink' => true],
                'pay_sys_id' => [S2pOrdersPaySysColumn::class, 't'],
                'pay_class_id' => [S2pOrdersPayClassColumn::class, 't'],
                'device' => [S2pOrdersDeviceColumn::class, 'o'],
                'country' => [CountryColumn::class, 'o'],
                'ip' => [IpColumn::class, 'o'],
                'requisite' => [RequisiteColumn::class, 'o'],
                'invoice_id' => [S2pOrderInvoiceIdColumn::class, 'o'],
                'ignore' => [IgnoreColumn::class, 'o'],
                'instance_id' => [S2pInstanceColumn::class, 'o'],
            ],
            'Bin' => [
                'bin_country' => [CountryColumn::class, 'b', 'title' => 'Issuer country'],
                'bin_card' => [BinColumn::class, 'o', 'title' => 'Bin'],
                'bin_card_global_pay_system' => [BinGlobalPaysysColumn::class, 'b'],
            ],
            'Regs' => [
                'location' => [LocationColumn::class, 'u'],
            ]
        ];
    }

    public function columns(): array
    {
        return $this->getColumnsFiltered();
    }

    public function metrics(): array
    {
        return [
            'Transactions' => [
                'count' => [CountColumn::class, 't', 'title' => 'Count'],
                'amount_usd' => [MoneyColumn::class, ['expr' => "SUM(t.amount_usd)", 't'], 'title' => 'Sum USD'],
                'amount_eur' => [MoneyColumn::class, ['expr' => "SUM(t.amount_eur)", 't'], 'title' => 'Sum EUR'],
            ]
        ];
    }

    public function groups(): array
    {
        return $this->getColumnsFiltered(true);
    }

    private function getColumnsFiltered(bool $grouping = false): array
    {
        $columns = [
            'Transactions' => array_merge(
                [
                    'order_id' => [S2pOrdersIdColumn::class, ['t' => 'order_id'], 'decorateS2pLink' => true],
                    'transaction_id' => [S2pTransactionIdColumn::class, 't'],
                    'user_id' => [UserIdColumn::class, 'o', 'decorateWithS2pUserLink' => true, 'isHtmlValue' => true],
                    'user_сid' => [UserCidColumn::class, 'u'],
                    'site_id' => [SiteIdColumn::class, 't'],
                    'project_id' => [S2pProjectIdColumn::class, 't'],
                    'invoice_id' => [S2pOrderInvoiceIdColumn::class, 'o'],
                    'pay_sys_id' => [S2pOrdersPaySysColumn::class, 't'],
                    'pay_source' => [S2pOrdersPaySourceColumn::class, 'o'],
                    'status' => [S2pOrdersStatusColumn::class, 't'],
                    'status_id' => [S2pOrdersStatusIdColumn::class, 't'],
                    'type' => [S2pTransactionTypeColumn::class, 't'],
                    'dir' => [S2pTransactionDirColumn::class, 't'],
                    'pay_type' => [S2pOrdersPayTypeColumn::class, 'o'],
                    'instance_id' => [S2pInstanceColumn::class, 'o'],
                ],
                $grouping ? [] : [
                    'amount_usd' => [MoneyColumn::class, ['t' => 'amount_usd'], 'title' => 'Sum USD'],
                    'amount_eur' => [MoneyColumn::class, ['t' => 'amount_eur'], 'title' => 'Sum EUR'],
                ],
                $grouping ? [] : ['is_ignore' => [BooleanColumn::class, ['expr' => '(o.user_id IS NOT NULL)', 'o'], 'title' => 'Is ignore']],
                [
                    'requisite' => [RequisiteColumn::class, 'o'],
                    'ip' => [IpColumn::class, 'o', 'decorateS2pLink' => true, 'isHtmlValue' => true],
                    'country' => [CountryColumn::class, 'o'],
                    'country_real' => [CountryRealColumn::class, ['ps', 'b'], 'binTable' => 'b', 'paySysTable' => 'ps', 'ordersTable' => 'o', 'title' => 'Country real'],
                    'device' => [S2pOrdersDeviceColumn::class, 'o', 'title' => 'Platform Group (S2P)'],
                ],
            ),
            'Dates' => $grouping ? [
                    'month_updated' => [MonthColumn::class, ['t' => 'updated_at'], 'title' => 'Month (mod)'],
                    'day_updated' => [DayColumn::class, ['t' => 'updated_at'], 'title' => 'Day (mod)'],
                    'month_created' => [MonthColumn::class, ['t' => 'created_at'], 'title' => 'Month (cr)'],
                    'day_created' => [DayColumn::class, ['t' => 'created_at'], 'title' => 'Day (cr)'],
                    'month_success' => [MonthColumn::class, ['t' => 'success_at'], 'title' => 'Month (suc)'],
                    'day_success' => [DayColumn::class, ['t' => 'success_at'], 'title' => 'Day (suc)'],
                ] : [
                    'date_updated' => [DateColumn::class, ['t' => 'updated_at'], 'Date (mod)'],
                    'date_created' => [DateColumn::class, ['t' => 'created_at'], 'title' => 'Date (cr)'],
                    'date_success' => [DateColumn::class, ['t' => 'success_at'], 'title' => 'Date (suc)'],
                ],
            'Bin' => [
                'bin_system' => [BinGlobalPaysysColumn::class, 'b'],
                'bin_bank' => [BinBankColumn::class, 'b'],
                'bin_type' => [BinTypeColumn::class, 'b'],
                'bin_status' => [BinStatusColumn::class, 'b'],
                'bin_country' => [BinCountryColumn::class, 'b'],
                'bin_currency' => [BinCurrencyColumn::class, 'b'],
                'bin_card' => [BinColumn::class, 'o'],
            ],
            'Regs' => [
                'location' => [LocationColumn::class, 'u'],
            ]
        ];

        if ($this->canViewS2pPayClasses) {
            $columns['Transactions'] = array_merge($columns['Transactions'], [
                'pay_class_id' => [S2pOrdersPayClassColumn::class, 't'],
            ]);
        }

        if ($this->canViewS2pMids) {
            $columns['Transactions'] = array_merge($columns['Transactions'], [
                'mid_id' => [S2pOrdersMidColumn::class, 'o'],
            ]);
        }

        if ($this->canViewS2pTrustScore) {
            $columns['Transactions'] = array_merge($columns['Transactions'], [
                'trust_score' => [S2pOrdersTrustScoreColumn::class, 'o'],
                'trust_score_now' => [UsersS2pTrustScore::class, 'u'],
            ]);
        }

        if ($this->canViewS2pTrustGroups) {
            $columns['Transactions'] = array_merge($columns['Transactions'], [
                'trust_requisite' => [S2pOrdersTrustLevelColumn::class, ['o' => 'trust_requisite'], 'title' => 'Trust (requisite)'],
                'trust_user' => [S2pOrdersTrustLevelColumn::class, ['o' => 'trust_user'], 'title' => 'Trust (user)'],
                'trust_login' => [S2pOrdersTrustLevelColumn::class, ['o' => 'trust_login'], 'title' => 'Trust (login)'],
            ]);
        }

        return $columns;
    }

    public function tableMap(): array
    {
        return [
            't' => [S2pTransactions::TABLE_NAME],
            'o' => [S2pOrders::TABLE_NAME, 'o.id = t.order_id', ['t'], 'INNER JOIN'],
            'u' => [Users::TABLE_NAME, 'u.site_id = o.site_id AND u.user_id = o.user_id', ['o']],
            'ui' => [UserIgnoreIds::TABLE_NAME, 'ui.site_id = o.site_id AND ui.user_id = o.user_id', ['o']],
            'ps' => [S2pPaySystems::TABLE_NAME, 'ps.id = o.pay_sys_id', ['o']],
            'b' => [Bins::TABLE_NAME, 'b.bin = ' . Bin::binExpressionOfRequisite(), ['o']],
        ];
    }
}
