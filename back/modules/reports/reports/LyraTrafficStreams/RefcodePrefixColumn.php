<?php

namespace app\back\modules\reports\reports\LyraTrafficStreams;

use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\Refcodes;
use Yiisoft\Db\Connection\ConnectionInterface;
use app\back\modules\reports\columns\Filtered;

class RefcodePrefixColumn extends BaseColumn implements Filtered, Selected
{
    public string $column = 'code';
    public string $title = 'Refcode Prefix';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andHaving(Refcodes::prefixAggFilterExpression($db, Str::explodeText($value), $this->tableAlias, $this->column));
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return Refcodes::prefixAggExpression($this->tableAlias);
    }


}
