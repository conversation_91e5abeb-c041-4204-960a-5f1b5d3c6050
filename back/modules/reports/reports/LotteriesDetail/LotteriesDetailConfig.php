<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\LotteriesDetail;

use app\back\components\helpers\DateHelper;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\LotteryTicketIdColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Lotteries;
use app\back\repositories\LotteryTickets;
use app\back\repositories\LotteryWins;

class LotteriesDetailConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            [['start_at', 'site_id'], 'required']
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['start_at', DateHelper::monthBegin(), '>='],
            ['finish_at', DateHelper::yesterday(), '<='],
            ['site_id', []],
            ['metrics', ['date']],
            ['groups', ['user_id', 'site_id', 'user_name', 'is_distributed', 'ticket_id', 'lottery_id', 'info']],
        ];
    }

    public function filters(): array
    {
        return [
            'Main' => [
                'start_at' => [DateColumn::class, ['l' => 'start_at'], 'title' => 'Started at'],
                'finish_at' => [DateColumn::class, ['l' => 'finish_at'], 'title' => 'Finished at'],
                'user_id' => [UserIdColumn::class, 'lt'],
                'site_id' => [SiteIdColumn::class, 'l'],
                'site_user' => [SiteUserColumn::class, 'lt'],
                'lottery_id' => [LotteryWinLotteryIdColumn::class, 'lw'],
                'ticket_id' => [LotteryTicketIdColumn::class, 'lw'],
            ]
        ];
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        return [
            'Main' => [
                'date' => [SimpleColumn::class, ['expr' => "string_agg(updated_at::text, '<br>')"], 'title' => 'Date', 'isHtmlValue' => true],
            ]
        ];
    }

    public function groups(): array
    {
        return [
            'Main' => [
                'user_id' => [UserIdColumn::class, 'lt'],
                'site_id' => [SiteIdColumn::class, 'lt'],
                'site_user' => [SiteUserColumn::class, 'lt'],
                'ticket_id' => [LotteryTicketIdColumn::class, 'lt'],
                'lottery_id' => [LotteryWinLotteryIdColumn::class, 'lw'],
                'user_name' => [SimpleColumn::class, ['expr' => "((lw.info)->>'user_name')", 'l'], 'title' => 'User login'],
                'is_distributed' => [SimpleColumn::class, ['expr' => 'is_distributed', 'lw'], 'title' => 'Status'],
                'info' => [SimpleColumn::class, ['lw' => 'info'], 'title' => 'Info', 'isHtmlValue' => true],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'lw' => [LotteryWins::TABLE_NAME],
            'lt' => [LotteryTickets::TABLE_NAME, 'lt.site_id = lw.site_id AND lt.ticket_id = lw.ticket_id AND lt.lottery_id = lw.lottery_id'],
            'l' => [Lotteries::TABLE_NAME, 'l.site_id = lt.site_id AND l.lottery_id = lt.lottery_id', ['lt']],
        ];
    }
}
