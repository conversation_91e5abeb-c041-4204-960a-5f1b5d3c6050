<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\StpEvents;

use app\back\components\validators\IntArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\StpPublishers;

class StpEventPublisherIdColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'publisher_id';
    public string $title = 'Publisher id';

    public function __construct(private readonly StpPublishers $stpPublishersRepo)
    {
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, $this->stpPublishersRepo->getNames()];
    }

    public function inputProps(): array
    {
        return [

            'type' => 'select',
            'list' => $this->stpPublishersRepo->getIdNameDict(),
        ];
    }

    public function decorate($value, array $row)
    {
        return $this->stpPublishersRepo->getNameById($value);
    }
}
