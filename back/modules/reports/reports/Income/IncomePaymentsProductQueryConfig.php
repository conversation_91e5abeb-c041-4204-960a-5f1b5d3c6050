<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Income;

use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateRangeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;

class IncomePaymentsProductQueryConfig extends BaseQueryConfig
{
    public function selects(): array
    {
        $currencyField = UserTransaction::AMOUNT_COLUMN_BY_CURRENCY[$this->request->getFilter('currency')];

        $in = UserTransaction::DIR_IN;

        return [
            /** @see IncomePaymentsS2pQueryConfig::selects() Same order matters (for union) */
            'site_id' => [SimpleColumn::class, ['us' => 'site_id']],
            'project_id' => [SimpleColumn::class, ['expr' => '(null::int)', 'us']],
            'day' => [DayColumn::class, ['us' => 'updated_at']],
            'in' => [MoneyColumn::class, ['expr' => "SUM(us.$currencyField) FILTER (WHERE us.dir = $in)", 'us']],
            'in_out' => [MoneyColumn::class, ['expr' => "SUM(us.$currencyField * us.dir)", 'us']],
            'firsts' => [CountColumn::class, ['expr' => "COUNT(*) FILTER (WHERE us.is_first_success)", 'us']],
            'location' => [LocationColumn::class, ['users']],
        ];
    }

    public function filters(): array
    {
        return [
            'date_range' => [DateRangeColumn::class, ['us' => 'updated_at']],
            'site_id' => [SiteIdColumn::class, 'us'],
            'currency' => [CurrencyColumn::class, 'us'],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'us'],
            'location' => [LocationColumn::class, ['users']],
            'day' => [DayColumn::class, ['us' => 'updated_at']],
        ];
    }

    public function tableMap(): array
    {
        return [
            'us' => [UserTransactions::TABLE_NAME],
            'users' => [Users::TABLE_NAME, 'users.user_id = us.user_id AND users.site_id = us.site_id', ['us'], 'INNER JOIN'],
        ];
    }

    protected function beforeQuery(): void
    {
        $this->query->andWhere([
            'us.status' => UserTransaction::STATUS_SUCCESS,
            'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
        ]);

        UserIgnoreIds::excludeUsers($this->query, 'us');
    }
}
