<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusLog;

use app\back\components\helpers\Arr;
use app\back\components\validators\StringArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\BonusLogs;
use Psr\SimpleCache\CacheInterface;
use Yiisoft\Db\Connection\ConnectionInterface;

class BonusLogBonusColumn extends BaseColumn implements Selected, Filtered, Decorated, Operators
{
    public string $title = 'Bonus';
    public string $column = 'bonus';

    public function __construct(
        private readonly BonusLogs $bonusLogsRepo,
        private readonly CacheInterface $cache,
    ) {
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'multiple' => true,
            'list' => Arr::assocToIdName($this->bonusLogsRepo->getBonuses($this->cache)),
        ];
    }

    public function rule(): array
    {
        return [StringArrayValidator::class, $this->bonusLogsRepo->getBonuses($this->cache)];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function operators(): array
    {
        return static::IN_NOT_IN;
    }

    public function decorate($value, array $row)
    {
        return $value;
    }
}
