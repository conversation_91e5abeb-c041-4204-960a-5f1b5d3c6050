<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\LoginTokens;

use app\back\modules\reports\columns\EmailColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;

class LoginTokensConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            ['site_id', 'required']
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['site_id', []],
            ['columns', ['site_id', 'user_id', 'login_token']],
        ];
    }

    public function tableMap(): array
    {
        return [
            'usi' => [UserSpecialInfos::TABLE_NAME],
            'u' => [Users::TABLE_NAME, 'u.site_id = usi.site_id AND u.user_id = usi.user_id'],
        ];
    }

    public function filters(): array
    {
        return [
            'Login tokens' => [
                'site_id' => [SiteIdColumn::class, 'usi'],
                'user_id' => [UserIdColumn::class, 'usi'],
                'email' => [EmailColumn::class, 'u'],
                'login_token' => [LoginTokenColumn::class, 'usi'],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            'Login' => [
                'site_id' => [SiteIdColumn::class, 'usi'],
                'user_id' => [UserIdColumn::class, 'usi'],
                'login_token' => [LoginTokenColumn::class, 'usi'],
            ],
        ];
    }

    public function metrics(): array
    {
        return [];
    }

    public function groups(): array
    {
        return [];
    }
}
