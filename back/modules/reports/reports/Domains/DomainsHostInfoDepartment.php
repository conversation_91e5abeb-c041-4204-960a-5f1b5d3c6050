<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Domains;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\HostInfo;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class DomainsHostInfoDepartment extends BaseColumn implements Decorated, Selected, Filtered
{
    use FilterAndSelectDefault;

    public string $title = 'Department';
    public string $column = 'department';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(HostInfo::DEPARTMENTS),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, HostInfo::DEPARTMENTS];
    }

    public function decorate($value, array $row)
    {
        return HostInfo::getDepartmentNameById($value);
    }
}
