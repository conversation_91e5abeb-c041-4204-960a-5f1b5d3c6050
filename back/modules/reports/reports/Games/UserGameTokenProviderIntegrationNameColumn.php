<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Games;

use app\back\components\validators\StringArrayValidator;
use app\back\entities\enums\UgtProviderIntegrationName;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class UserGameTokenProviderIntegrationNameColumn extends BaseColumn implements Selected, Filtered
{
    use FilterAndSelectDefault;

    public string $column = 'provider_integration_name';
    public string $title = 'Provider integration name';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => UgtProviderIntegrationName::idNameList(),
        ];
    }

    public function rule(): array
    {
        return [StringArrayValidator::class, UgtProviderIntegrationName::values(), true];
    }
}
