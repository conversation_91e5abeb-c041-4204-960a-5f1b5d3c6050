<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\UsersLimits;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\UserLimit;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class UsersLimitsTypeColumn extends BaseColumn implements Filtered, Selected, Decorated
{
    use FilterAndSelectDefault;

    public string $title = 'Limit type';
    public string $column = 'limit_type';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(UserLimit::LIMIT_TYPES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, UserLimit::LIMIT_TYPES];
    }

    public function decorate($value, array $row): string
    {
        return UserLimit::LIMIT_TYPES[$value] ?? '';
    }
}
