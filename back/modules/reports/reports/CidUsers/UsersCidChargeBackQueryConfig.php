<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\CidUsers;

use app\back\entities\S2pTransaction;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pTransactions;

class UsersCidChargeBackQueryConfig extends BaseQueryConfig
{
    protected function beforeQuery(): void
    {
        $this->request
            ->select('site_id', 'user_id')
            ->group('site_id', 'user_id');

        $usersTable = CidUsersConfig::USERS_TABLE;
        $this->query
            ->andWhere("{$usersTable}.site_id = s2p_o.site_id AND {$usersTable}.user_id = s2p_o.user_id")
            ->andWhere([
                's2p_t.type' => S2pTransaction::TYPE_CHARGEBACK,
                's2p_t.status' => S2pTransaction::STATUS_SUCCESS,
            ]);
    }

    public function selects(): array
    {
        return [
            'sum_cb_eur' => [SimpleColumn::class, ['expr' => 'SUM(s2p_t.amount_eur)', 's2p_t']],
            'cb_count' => [SimpleColumn::class, ['expr' => 'COUNT(*)', 's2p_t']],
            ...$this->groups(),
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 's2p_o'],
            'user_id' => [UserIdColumn::class, 's2p_o'],
        ];
    }

    public function filters(): array
    {
        return [];
    }

    public function tableMap(): array
    {
        return [
            's2p_o' => [S2pOrders::TABLE_NAME],
            's2p_t' => [S2pTransactions::TABLE_NAME, 's2p_t.order_id = s2p_o.id'],
        ];
    }
}
