<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\CidSegments;

use app\back\entities\S2pOrder;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\S2pOrders;
use app\back\repositories\Users;

class CidSegmentsInQueryConfig extends BaseQueryConfig
{
    public function groups(): array
    {
        return [
            'month' => [MonthColumn::class, 'o'],
            'cid' => [UserCidColumn::class, 'u'],
        ];
    }

    public function selects(): array
    {
        return [
            'month' => [MonthColumn::class, 'o'],
            'in' => [SimpleColumn::class, ['expr' => 'SUM(o.summ_rub)', 'o']]
        ];
    }

    public function filters(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'o'],
            'month' => [MonthColumn::class, 'o'],
        ];
    }

    public function beforeQuery(): void
    {
        $this->request->group('cid');
        $this->query
            ->andWhere([
                'AND',
                ['=', 'o.status', S2pOrder::STATUS_SUCCESS],
                ['=', 'o.type', S2pOrder::TYPE_IN],
                ['IS NOT', 'u.cid', null]
            ]);
    }

    public function tableMap(): array
    {
        return [
            'u' => [Users::TABLE_NAME],
            'o' => [S2pOrders::TABLE_NAME, 'o.site_id = u.site_id AND o.user_id = u.user_id', ['u'], 'INNER JOIN'],
        ];
    }
}
