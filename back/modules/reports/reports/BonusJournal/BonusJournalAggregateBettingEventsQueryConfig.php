<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusJournal;

use app\back\entities\BonusJournal;
use app\back\modules\reports\columns\SimpleColumn;

class BonusJournalAggregateBettingEventsQueryConfig extends BonusJournalAggregateQueryConfig
{
    protected function distinctColumns(): array
    {
        return ['site_id', 'program_id', 'betting_bet_id', 'event_type'];
    }

    public function metrics(): array
    {
        $isBetLoss = "{$this->nestedAlias()}.event_type = " . BonusJournal::EVENT_BET_LOSS;
        $isBetAccept = "{$this->nestedAlias()}.event_type = " . BonusJournal::EVENT_ACCEPT_BET;
        $isBetWin = "{$this->nestedAlias()}.event_type = " . BonusJournal::EVENT_BET_WIN;

        return [
            'betting_loss' => [SimpleColumn::class, ['expr' => "COUNT(*) FILTER (WHERE $isBetLoss)", "{$this->nestedAlias()}:n" => 'event_type']],
            'betting_accept' => [SimpleColumn::class, ['expr' => "COUNT(*) FILTER (WHERE $isBetAccept)", "{$this->nestedAlias()}:n" => 'event_type']],
            'betting_win' => [SimpleColumn::class, ['expr' => "COUNT(*) FILTER (WHERE $isBetWin)", "{$this->nestedAlias()}:n" => 'event_type']],
        ];
    }
}
