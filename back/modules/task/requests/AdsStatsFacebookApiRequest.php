<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\components\HttpClient;
use app\back\components\Notifier;
use app\back\components\parsers\JsonParser;
use app\back\entities\AdCredential;
use app\back\modules\task\requests\exceptions\AuthException;
use app\back\modules\task\requests\exceptions\ClientException;
use app\back\repositories\AdCredentials;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

/**
 * @see https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/
 * @see https://developers.facebook.com/tools/explorer/
 * @see https://docs.keitaro.io/ru/third-party-integrations/facebook.html
 */
class AdsStatsFacebookApiRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;
    use AdsCredentialsInvalidateTrait;

    public const string ACTION_INSTALL = 'mobile_app_install';
    public const string ACTION_REGISTRATION = 'complete_registration';
    public const string ACTION_DEPOSIT = 'purchase';

    private const int FB_MISSING_PERMISSIONS = 100; // accessToken require permissions ads_read, read_insight
    private const int FB_TOKEN_EXPIRED = 190;
    private const int FB_AUTH_ERROR = 200;

    public AdCredentials $adCredentialsRepo;
    public Notifier $notifier;
    public array $credentials;
    public string $from;
    public string $to;

    protected function fetchData(): iterable
    {
        foreach ($this->credentials as ['credential' => $credential, 'proxy' => $proxy]) {
            /** @var AdCredential $credential */
            $apiCredentials = $credential->getValueObject();

            /** To skip any other Facebook credentials like Renta */
            if (!isset($apiCredentials->accountId, $apiCredentials->accessToken)) {
                $this->log->debug("Credential {$credential->id} skipped as absent 'account_id' or 'access_token'");
                continue;
            }

            $url = $this->buildUrl([
                'accountId' => $apiCredentials->accountId,
                'accessToken' => $apiCredentials->accessToken,
            ]);

            $client = $this->createHttpClient([
                'proxy' => $proxy?->config,
            ]);

            do {
                $response = $this->getResponse($client, $url, $proxy, $credential);
                if ($response === null) {
                    continue 2; // process next account when credential or proxy failed
                }

                $content = $this->parse($response->getContent(false));
                foreach ($content['data'] as $row) {
                    $row = $this->convertOne($row);
                    $row['credential_id'] = $credential->id;
                    yield $row;
                }
                $url = $content['paging']['next'] ?? null; // contains url to the next page
            } while ($url !== null);

            $this->adCredentialsRepo->updateLastLoad($credential);
        }

        if (count($this->invalidatedCredentials) > 0) {
            $this->notifier->notify('invalid_ads_credentials_facebook', 'Some facebook credential(s) invalidated', implode("<br>", $this->invalidatedCredentials));
        }
    }

    protected function buildUrl(array $params = []): string
    {
        $queryParams = http_build_query([
            'level' => 'ad',
            'fields' => 'account_currency,account_id,account_name,ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name,clicks,impressions,spend,actions',
            'time_range' => $this->buildTimeRange(),
            'filtering' => $this->buildFilter(),
            'access_token' => $params['accessToken'],
            'limit' => 200,
            'time_increment' => 1, // breakdown: 1 day
        ]);

        return "https://graph.facebook.com/v20.0/act_{$params['accountId']}/insights?$queryParams";
    }

    private function buildTimeRange(): string
    {
        return Json::encode([
            'since' => date(DateHelper::DATE_FORMAT_PHP, strtotime($this->from)),
            'until' => date(DateHelper::DATE_FORMAT_PHP, strtotime($this->to))
        ]);
    }

    private function buildFilter(): string
    {
        return Json::encode([[
            'field' => 'action_type',
            'operator' => 'IN',
            'value' => [self::ACTION_INSTALL, self::ACTION_DEPOSIT, self::ACTION_REGISTRATION],
        ]]);
    }

    protected function parserConfig(): string|array
    {
        return JsonParser::class;
    }

    private function parseFacebookResponse(ResponseInterface $response): array
    {
        $content = $this->parse($response->getContent(false));
        return [$content['error']['code'] ?? null, $content['error']['message'] ?? null];
    }

    private function getResponse(HttpClient $client, string $url, mixed $proxy, AdCredential $credential): ?ResponseInterface
    {
        $response = null;
        try {
            $response = $client->get($url);
            $this->handleStatusCode($response);
        } catch (AuthException $e) {
            [$facebookErrorCode, $facebookErrorMessage] = $this->parseFacebookResponse($response);
            $errorMessage = $facebookErrorMessage ?? 'Unspecified auth error';
            $this->log->notice("$errorMessage in account {$credential->name}, code: $facebookErrorCode");
            return null;
        } catch (TransportExceptionInterface $e) {
            $this->invalidateProxyOnError($e, $proxy, $credential);
            return null;
        } catch (ClientException $e) {
            [$facebookErrorCode, $facebookErrorMessage] = $this->parseFacebookResponse($response);
            if (in_array($facebookErrorCode, [self::FB_TOKEN_EXPIRED, self::FB_AUTH_ERROR], true)) {
                $this->invalidateCredential($credential, $facebookErrorMessage);
                return null;
            }

            if ($facebookErrorCode === self::FB_MISSING_PERMISSIONS) {
                $this->log->notice("Missing permission in account {$credential->name}");
                return null;
            }

            throw $e;
        }

        return $response;
    }
}
