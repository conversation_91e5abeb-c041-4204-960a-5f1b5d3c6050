<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\Initializable;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\HttpClient\ResponseInterface;

abstract class BaseRequest
{
    public ?string $debugFile = null;

    protected readonly LoggerInterface $log;

    abstract protected function fetchData(): iterable;
    abstract protected function responseToData(ResponseInterface $response, ?string $path = null): iterable;

    #[Initializable]
    final public function initLog(LoggerInterface $log): void
    {
        $this->log = $log;
    }

    public function finalData(): iterable
    {
        if ($this->debugFile !== null) {
            $content = file_get_contents($this->debugFile);
            if ($content === false) {
                throw new \InvalidArgumentException("File get content failed. Path: $this->debugFile");
            }
            $response = MockResponse::fromRequest('', '', [], new MockResponse($content));
            return $this->responseToData($response);
        }

        return $this->fetchData();
    }
}
