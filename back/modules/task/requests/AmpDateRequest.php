<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\parsers\CsvParser;

class AmpDateRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;

    public string $authKey;
    public string $date;

    protected function fetchData(): iterable
    {
        $date = date('Y-m-d', strtotime($this->date));
        $signature =  md5($date . '*' . $this->authKey);

        $url = $this->buildUrl([
            ':date' => $date,
            ':signature' => $signature,
        ]);

        return $this->responseToData($this->createHttpClient()->get($url));
    }

    protected function parserConfig(): string|array
    {
        return CsvParser::class;
    }
}
