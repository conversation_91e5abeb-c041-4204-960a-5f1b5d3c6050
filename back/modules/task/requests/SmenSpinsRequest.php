<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\helpers\Json;

class SmenSpinsRequest extends SmenRequest
{
    public int $userId;
    public string $from;
    public string $to;

    protected function params(): array
    {
        return [
            'from' => date('Y-m-d H:i:s', strtotime($this->from)),
            'to' => date('Y-m-d H:i:s', strtotime($this->to)),
            'userId' => $this->userId
        ];
    }

    protected function fetchData(): iterable
    {
        foreach (parent::fetchData() as $row) {
            if (!empty($row['symbols'])) {
                $row['symbols'] = Json::decode($row['symbols']);
            } else {
                $row['symbols'] = null;
            }

            yield $row;
        }
    }
}
