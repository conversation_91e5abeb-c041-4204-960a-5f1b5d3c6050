<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\parsers\JsonParser;
use Symfony\Contracts\HttpClient\ResponseInterface;

class JiraRequest extends BaseRequest
{
    use RequestWithHttpClient {
        buildUrl as innerBuildUrl;
    }

    use RequestWithParserAndConverter {
        responseToData as innerResponseToData;
    }

    private const int PAGE_SIZE = 1000;
    private const string ISSUES_IN_RESPONSE = 'issues';

    public string $authKey;
    public string $jql;
    public string $fields;
    public string $expand;

    protected function buildUrl(array $params = []): string
    {
        $url = $this->innerBuildUrl([
            ':jql' => $this->jql,
            ':fields' => $this->fields,
            ':expand' => $this->expand,
        ]);

        return $this->addQueryParams($url, [
            'startAt' => $params['pageStart'],
            'maxResults' => self::PAGE_SIZE,
        ]);
    }

    protected function fetchData(): iterable
    {
        $pageStart = 0;
        do {
            $response = $this->createHttpClient()->get($this->buildUrl(['pageStart' => $pageStart]), [
                'auth_basic' => $this->authKey,
            ]);

            $dataWithIssues = $this->innerResponseToData($response, self::ISSUES_IN_RESPONSE);
            yield from $dataWithIssues[self::ISSUES_IN_RESPONSE];

            $pageStart += (int) $dataWithIssues['maxResults'];
        } while ($pageStart < $dataWithIssues['total']);
    }

    /** @internal intended for phpunit tests only */
    protected function responseToData(ResponseInterface $response, ?string $path = null): iterable
    {
        $dataWithIssues = $this->innerResponseToData($response, self::ISSUES_IN_RESPONSE);
        yield from $dataWithIssues[self::ISSUES_IN_RESPONSE];
    }

    protected function parserConfig(): string|array
    {
        return JsonParser::class;
    }
}
