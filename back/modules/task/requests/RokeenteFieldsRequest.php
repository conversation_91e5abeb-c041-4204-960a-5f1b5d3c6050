<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\parsers\CsvParser;
use app\back\entities\RokeenteBlacklist;

class RokeenteFieldsRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;

    public string $authKey;

    protected function fetchData(): iterable
    {
        $client = $this->createHttpClient();

        $url = $this->buildUrl();

        $response = $client->post($url, ['api_token' =>  $this->authKey]);

        return array_filter(
            $this->responseToData($response),
            fn($r) => RokeenteBlacklist::isItBlacklistField($r['field'])
        );
    }

    protected function parserConfig(): string|array
    {
        return [
            'class' => CsvParser::class,
            'escape' => '',
        ];
    }
}
