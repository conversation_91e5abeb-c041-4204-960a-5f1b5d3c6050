<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\UserLogins;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersTransactionsLoginTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    public function process(): void
    {
        foreach ($this->getQuery()->each(1000) as $row) {
            $this->totalRows++;
            $this->affectedRows += $this->db->createCommand()->update(
                UserTransactions::TABLE_NAME,
                [
                    'login_id' => $row['login_id'],
                ],
                [
                    'site_id' => $row['site_id'],
                    'transaction_id' => $row['transaction_id'],
                ]
            )->execute();
        }
    }

    protected function getQuery(): Query
    {
        $siteId = $this->siteIdResolver->siteId();

        return (new Query($this->db))
            ->select([
                'us.site_id',
                'us.transaction_id',
                'login_id' => '(ARRAY_AGG(ua.login_id ORDER BY ua.date DESC))[1]'
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(
                ['ua' => UserLogins::TABLE_NAME],
                "ua.site_id = us.site_id AND ua.user_id = us.user_id AND ua.date BETWEEN us.created_at - INTERVAL '1 month' AND us.created_at"
            )
            ->where([
                'us.site_id' => $siteId,
                'ua.success' => true,
            ])
            ->andWhere(['>=', 'us.updated_at', $this->from])
            ->andWhere(['<', 'us.updated_at', $this->to])
            ->groupBy(['us.site_id', 'us.transaction_id'])
            ->having(['IS DISTINCT FROM', 'us.login_id', new Expression('(ARRAY_AGG(ua.login_id ORDER BY ua.date DESC))[1]')]);
    }
}
