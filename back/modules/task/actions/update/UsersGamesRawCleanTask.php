<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\UserGameRaws;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersGamesRawCleanTask extends BaseTask
{
    public string $storeInterval;

    public function __construct(
        protected readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    public function process(): void
    {
        $interval = \DateInterval::createFromDateString($this->storeInterval);
        $to = (new \DateTime($this->to))->sub($interval);
        $this->log->info('Clean <= ' . $to->format('Y-m-d H:i:s'));

        $query = (new Query($this->db))
            ->select(['spin_id'])
            ->from(['r' => UserGameRaws::TABLE_NAME])
            ->where([
                'AND',
                ['site_id' => $this->siteIdResolver->siteId()],
                ['<=', 'r.created_at', $to->format('Y-m-d H:i:s')]
            ]);

        foreach ($query->batch(500) as $records) {
            $ids = Arr::getColumnAssoc($records, 'spin_id');
            $this->totalRows += count($ids);
            $this->affectedRows += (new Query($this->db))
                ->createCommand()
                ->delete(UserGameRaws::TABLE_NAME, [
                    'AND',
                    ['site_id' => $this->siteIdResolver->siteId()],
                    ['spin_id' => $ids]
                ])->execute();
        }
    }
}
