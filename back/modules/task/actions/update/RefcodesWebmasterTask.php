<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\modules\task\BaseTask;
use app\back\repositories\Refcodes;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class RefcodesWebmasterTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    // Working with WP refcodes
    // Example: wp_w8456p143_kp-brand-old => affiliate_id=8456
    public function process(): void
    {
        $codeRegexp = Refcodes::webmasterIdExp();

        $newCodes = (new Query($this->db))
            ->select([
                'code',
            ])
            ->from(['r' => Refcodes::TABLE_NAME])
            ->where(['~*', 'code', $codeRegexp])
            ->andWhere([
                'webmaster_id' => null,
            ]);

        foreach ($newCodes->each(1000) as $r) {
            if (preg_match("#{$codeRegexp}#i", $r['code'], $matches)) {
                $webmasterId = !empty($matches[1]) ? $matches[1] : ($matches[2] ?? null);

                $this->affectedRows += $this->db->createCommand()->update(Refcodes::TABLE_NAME, [
                    'webmaster_id' => $webmasterId,
                    'updated_at' => new Expression('NOW()'),
                ], [
                    'code' => $r['code'],
                ])->execute();
            }
        }

        $this->totalRows = $this->affectedRows;
    }
}
