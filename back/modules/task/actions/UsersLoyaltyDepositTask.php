<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\UserLoyaltyDeposits;

class UsersLoyaltyDepositTask extends ImportTask
{
    use TaskWithFromToRequest;

    public function __construct(
        private readonly UserLoyaltyDeposits $userLoyaltyDepositsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): UserLoyaltyDeposits
    {
        return $this->userLoyaltyDepositsRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        return true;
    }
}
