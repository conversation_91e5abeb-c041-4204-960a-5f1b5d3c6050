<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Container;
use app\back\components\Initializable;
use app\back\modules\reports\reports\PaymentsComparison\PaymentsComparisonConfig;
use app\back\modules\reports\reports\PaymentsComparison\UnifiedDirColumn;

class PaymentsComparisonTask extends BaseSendTask
{
    protected const array PARTS = [
        'In' => UnifiedDirColumn::DIR_IN,
        'Out' => UnifiedDirColumn::DIR_OUT,
    ];

    public array $siteIds;
    public string $titlePostfix = '';
    public string $contactName;

    private readonly Container $container;

    #[Initializable]
    final public function init(Container $container): void
    {
        $this->container = $container;
    }

    private function reportParams(string $dir): array
    {
        return [
            'columns' => ['site_id', 'user_id', 'transaction_id', 'order_id', 'date_s2p', 'date_product', 'status_s2p', 'status_product'],
            'isHtmlVersion' => true,
            'filters' => [
                ['site_id', $this->siteIds],
                ['updated_at', date('Y-m-d', $this->fromTime), '>='],
                ['updated_at', date('Y-m-d', $this->fromTime), '<='],
                ['dir', $dir],
            ],
        ];
    }

    public function getSubject(): string
    {
        $date = date('Y-m-d', $this->fromTime);

        return "Payments comparison: Sites vs S2P ($date)" . $this->titlePostfix;
    }

    protected function getContactName(): string
    {
        return $this->contactName;
    }

    public function getContent(): string
    {
        $tables = [];
        foreach (static::PARTS as $title => $dir) {
            ['data' => $data, 'columns' => $columns] = (new PaymentsComparisonConfig($this->container))->loadAndValidateOrException($this->reportParams($dir))->dataAndColumns();
            if ($data) {
                $tables[] = $this->table($data, $columns, $title);
            }
        }

        if (empty($tables)) {
            $tables[] = '<p>Платежи за день полностью синхронизированы.</p>';
        }

        return implode('', $tables);
    }
}
