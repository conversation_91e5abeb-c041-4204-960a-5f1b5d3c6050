<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Initializable;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\repositories\Sites;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class NotifyWeekendBigDepsTask extends BaseSendTask
{
    public string $parentPersonalManagerEmail;

    private const array GROUPS = [
        'Weekend Pre VIP yesterday deps 50+' => [
            'where' => [
                'u.status' => User::STATUS_PRE_VIP,
                'u.active_status' => User::ACTIVE_STATUS_ACTIVE,
            ],
            'having' => ['>=', 'SUM(us.amount_rub)', 50_000],
        ],
        'Weekend Awol VIP yesterday deps 50+' => [
            'where' => [
                'u.status' => [User::STATUS_VIP, User::STATUS_ULTRA],
                'u.active_status' => [User::ACTIVE_STATUS_AWOL, User::ACTIVE_STATUS_AWOL2],
            ],
            'having' => ['>=', 'SUM(us.amount_rub)', 50_000],
        ],
    ];

    private readonly Sites $sitesRepo;
    private readonly ConnectionInterface $db;

    #[Initializable]
    final public function init(Sites $sitesRepo, ConnectionInterface $db): void
    {
        $this->sitesRepo = $sitesRepo;
        $this->db = $db;
    }

    public function process(): void
    {
        $friday = date('Y-m-d', strtotime('friday this week', $this->fromTime));
        $saturday = date('Y-m-d', strtotime('saturday this week', $this->fromTime));

        if (!in_array(date('Y-m-d', $this->fromTime), [$friday, $saturday], true)) {
            return;
        }

        parent::process();
    }

    protected function getContactName(): string
    {
        return 'notify_weekend_big_deps';
    }

    public function getSubject(): string
    {
        return 'Weekend big dep users (' . date('Y-m-d', $this->fromTime) . ')';
    }

    public function getContent(): string
    {
        $today = date('Y-m-d', strtotime('+ 1 day', $this->fromTime));
        $yesterday = date('Y-m-d', $this->fromTime);

        $pmIds = $this->operatorsRepo->getSubordinatePersonalManagerIds($this->parentPersonalManagerEmail);

        $blocks = [];
        foreach (static::GROUPS as $title => $filters) {
            $data = (new Query($this->db))
                ->select([
                    'u.site_id',
                    'u.user_id',
                    'u.personal_manager',
                    'dep_sum_rub' => 'SUM(us.amount_rub)',
                ])
                ->from(['u' => Users::TABLE_NAME])
                ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'us.site_id = u.site_id AND us.user_id = u.user_id')
                ->where([
                    'AND',
                    [
                        'us.dir' => UserTransaction::DIR_IN,
                        'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                        'us.status' => UserTransaction::STATUS_SUCCESS,
                        'u.personal_manager' => $pmIds,
                    ],
                    $filters['where'],
                    ['>=', 'us.updated_at', $yesterday],
                    ['<', 'us.updated_at', $today],
                ])
                ->groupBy(['u.user_id', 'u.site_id'])
                ->having($filters['having'])
                ->orderBy(['u.date' => SORT_ASC])
                ->all();

            if (empty($data)) {
                continue;
            }

            $cols = [
                'site' => 'Site',
                'user_id' => 'User id',
                'personal_manager' => 'Personal manager',
                'dep_sum_rub' => 'Dep sum RUB',
            ];

            $title .= " ($yesterday)";

            foreach ($data as &$row) {
                $row['site'] = $this->sitesRepo->getNameById($row['site_id']);
                $row['user_id'] = User::playerLink($row['site_id'], $row['user_id']);
                $row['personal_manager'] = $this->operatorsRepo->getNameById($row['personal_manager']);
            }
            unset($row);

            $blocks[] = $this->table($data, $cols, $title);
        }

        if (empty($blocks)) {
            return 'no matched users';
        }

        return implode('<div class="mt-6"></div>', $blocks);
    }
}
