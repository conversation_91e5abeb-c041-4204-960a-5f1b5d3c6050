<?php

declare(strict_types=1);

namespace app\back\modules\task\components;

use app\back\entities\Requisite;
use app\back\repositories\Requisites;
use app\back\repositories\UserRequisites;
use Psr\Log\LoggerInterface;
use Yiisoft\Db\Query\Query;

class RequisiteAdder
{
    private const int MIN_LENGTH = 7;

    private bool $isLastRequisiteIsHashedCard = false;

    public function __construct(
        private readonly LoggerInterface $log,
        private readonly Requisites $requisitesRepo,
    ) {
    }

    public function add(string $requisite, int $type): ?int
    {
        return $this->tryImport($requisite, $type);
    }

    public function addFromUsersStats(string $requisite, int $siteId, int $userId): ?int
    {
        return $this->tryImport($requisite, siteId: $siteId, userId: $userId);
    }

    public function addFromS2pOrder(string $requisite, ?int $type, ?string $hash, int $siteId, int $userId): ?int
    {
        return $this->tryImport($requisite, Requisite::getTypeIdByS2pOrderType($type), $hash, $siteId, $userId);
    }

    public function addFromS2pAntifraudLog(string $requisite, ?int $type): ?int
    {
        return $this->tryImport($requisite, Requisite::getTypeIdByS2pOrderType($type));
    }

    public function addFromWebmasterEmail(string $requisite): ?int
    {
        return $this->tryImport($requisite, Requisite::TYPE_EMAIL);
    }

    public function addFromWebmasterPhone(string $requisite): ?int
    {
        return $this->tryImport($requisite, Requisite::TYPE_PHONE);
    }

    public function addFromWebmasterContact(string $requisite): ?int
    {
        return $this->tryImport($requisite, Requisite::TYPE_UNDEFINED_MESSENGER);
    }

    public function addFromWebmasterTelegramId(string $requisite): ?int
    {
        return $this->tryImport($requisite, Requisite::TYPE_TELEGRAM_ID);
    }

    public function addFromWebmasterTelegramUsername(string $requisite): ?int
    {
        return $this->tryImport($requisite, Requisite::TYPE_TELEGRAM_USERNAME);
    }

    private function tryImport(string $requisite, ?int $type = null, ?string $hash = null, ?int $siteId = null, ?int $userId = null): ?int
    {
        $this->isLastRequisiteIsHashedCard = false;

        $r = mb_strtolower(trim($requisite));
        if ($type === null) {
            $type = Requisite::detectType($r);
        }

        if ($type === null) {
            $this->log->warning("Requisite: $r type not detected");
            return null;
        }

        $r = Requisite::normalizeRequisite($r, $type);

        $this->isLastRequisiteIsHashedCard = Requisites::tryHashPostfix($r, $type, $hash);

        if ($r === null) {
            return null;
        }

        if (mb_strlen($r) < static::MIN_LENGTH) {
            return null;
        }

        $attrs = Requisite::importValidators()([
            'type' => $type,
            'requisite' => $r,
        ], $this->requisitesRepo);

        if ($attrs['requisite'] === null) {
            return null;
        }

        if (!isset($hash) && isset($siteId, $userId) && $type === Requisite::TYPE_CARD) {
            $id = $this->findEqualAlreadyUsedHashedOrSameRequisiteId($r, $siteId, $userId);

            if ($id !== false) {
                return $id;
            }
            $entity = null;
        } else {
            $entity = $this->requisitesRepo->findOne($attrs);
        }

        if ($entity === null) {
            $entity = new Requisite($attrs);
            $this->requisitesRepo->insert($entity);
        }

        return $entity->id;
    }

    public function isLastRequisiteIsHashedCard(): bool
    {
        return $this->isLastRequisiteIsHashedCard;
    }

    private function findEqualAlreadyUsedHashedOrSameRequisiteId(string $requisite, int $siteId, int $userId): int|false
    {
        // search cards with hash if current is without
        $likeWithHash = strtr($requisite, ['%' => '\%', '_' => '\_', '\\' => '\\\\']) . Requisites::CARD_HASH_DELIMITER . '%';

        return (new Query($this->requisitesRepo->db))
            ->select(['r.id'])
            ->from(['r' => Requisites::TABLE_NAME])
            ->leftJoin(['ur' => UserRequisites::TABLE_NAME], [
                'AND',
                'ur.requisite_id = r.id',
                [
                    'ur.site_id' => $siteId,
                    'ur.user_id' => $userId,
                ]
            ])
            ->where([
                'OR',
                ['AND', ['LIKE', 'r.requisite', $likeWithHash, null], ['IS NOT', 'ur.requisite_id', null]],
                ['r.requisite' => $requisite],
            ])
            ->orderBy(['length(r.requisite)' => SORT_DESC])
            ->limit(1)
            ->scalar();
    }
}
