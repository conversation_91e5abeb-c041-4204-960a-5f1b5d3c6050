<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\s2p;

use app\back\entities\S2pOrder;
use app\back\modules\api\params\ApiParamS2pProjectName;
use app\back\modules\api\params\ApiParamUserId;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pProjects;
use app\back\repositories\Users;
use Yiisoft\Db\Query\Query;

class UserCidStatsMethod extends S2pGetMethod
{
    use ApiParamS2pProjectName;
    use ApiParamUserId;

    public function run(): iterable
    {
        $request = $this->createRequest();

        $request->map([
            'project_name' => 'p.name',
            'user_id' => 'u.user_id',
        ], true);

        $query = (new Query($this->db))
            ->select([
                'cid' => 'u.cid',
                'in' => 'SUM(o.summ) FILTER (WHERE type = :in)',
                'out' => 'SUM(o.summ) FILTER (WHERE type = :out)',
            ])
            ->from(['p' => S2pProjects::TABLE_NAME])
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = p.site_id')
            ->innerJoin(['u2' => Users::TABLE_NAME], 'u2.cid = u.cid')
            ->innerJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = u2.site_id AND o.user_id = u2.user_id')
            ->where([
                'o.status' => S2pOrder::STATUS_SUCCESS
            ])
            ->groupBy([
                'u.cid',
            ])
            ->addParams([
                'in' => S2pOrder::TYPE_IN,
                'out' => S2pOrder::TYPE_OUT,
            ]);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }
}
