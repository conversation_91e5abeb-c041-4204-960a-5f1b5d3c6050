<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\marketing;

use app\back\components\validators\IdValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\Bin;
use app\back\entities\S2pOrder;
use app\back\modules\api\components\Operators;
use app\back\repositories\Bins;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPaySystems;
use Yiisoft\Db\Query\Query;

class TransactionInfoMethod extends BaseMarketingGetMethod
{
    #[Operators(Operators::EQ)]
    #[IdValidator]
    protected array $site_id;
    #[Operators(Operators::EQ)]
    #[StringValidator(0, 36)]
    public array $transaction_id;

    protected function run(): iterable
    {
        $request = $this->createRequest();

        $request->map([
            'site_id' => 'o.site_id',
            'transaction_id' => 'o.invoice_id',
        ]);

        $query = (new Query($this->db))
            ->select([
                'original_amount' => 'o.client_original_amount',
                'original_currency_code' => 'o.client_original_currency',
                'payment_system' => 'ps.code',
                'requisite' => 'o.requisite',
                'amount_usd' => 'o.summ',
                'status' => 'o.status',
                'card_issuer_country' => 'b.country',
                'card_holder' => 'o.card_holder',
                'card_global_payment_system' => 'b.system',
                'card_bank' => 'b.bank',
                'card_type' => 'b.type',
                'card_status' => 'b.status',
                'card_currency_code' => 'b.currency_code',
                'card_bin_number' => 'b.bin',
                'created_at' => 'o.date_created',
                'updated_at' => 'o.date',
            ]);

        $query
            ->from(['o' => S2pOrders::TABLE_NAME])
            ->leftJoin(['b' => Bins::TABLE_NAME], 'b.bin = ' . Bin::binExpressionOfRequisite('o'))
            ->leftJoin(['ps' => S2pPaySystems::TABLE_NAME], 'ps.id = o.pay_sys_id')
            ->where([
                'o.type' => S2pOrder::TYPE_IN,
            ]);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure
    {
        return static function ($row) {
            $row['status'] = S2pOrder::STATUSES[$row['status']];

            return $row;
        };
    }
}
