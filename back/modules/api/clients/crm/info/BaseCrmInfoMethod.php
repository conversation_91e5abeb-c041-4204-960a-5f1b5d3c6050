<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\info;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\CallableValidator;
use app\back\modules\api\clients\crm\BaseCrmGetMethod;
use app\back\modules\api\components\Operators;
use app\back\modules\api\params\ApiParamSiteId;
use Yiisoft\Db\Query\Query;

abstract class BaseCrmInfoMethod extends BaseCrmGetMethod
{
    use ApiParamSiteId;

    public bool $needUsersCheck = true;

    #[Operators(Operators::EQ_IN)]
    #[CallableValidator([self::class, 'userIdRequiredCheck'])]
    #[BigIdValidator]
    protected array $user_id = [];

    abstract public function columns(): array;
    abstract public function getMainQuery(): Query;
    abstract public function getMainQueryAlias(): string;

    public static function userIdRequiredCheck(mixed $value, self $form): ?string
    {
        if (($value === null || (is_array($value) && empty($value))) && $form->needUsersCheck) {
            return 'is required';
        }

        return null;
    }

    public function run(): iterable
    {
        $request = $this->createRequest();

        $alias = $this->getMainQueryAlias();

        $usersQuery = (new Query($this->db))
            ->select(['user_id'])
            ->from($this->createUsersTempTable($request));

        $usersAlias = static::USERS_TABLE_ALIAS;

        $query = $this->getMainQuery()
            ->withQuery($usersQuery, $usersAlias)
            ->select($this->columns())
            ->innerJoin($usersAlias, "$usersAlias.user_id = $alias.user_id");

        $request->map([
            'site_id' => "$alias.site_id",
        ], true);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function joinQuery(Query $query, int $siteId, string $alias, array $columnsMap = []): void
    {
        $infoAlias = $this->getMainQueryAlias();

        $select = [];

        foreach ($this->columns() as $columnAlias => $columnExpression) {
            if (array_key_exists($columnAlias, $columnsMap)) {
                $newAlias = $columnsMap[$columnAlias];
                $select[$newAlias] = $columnExpression;
            } else {
                $select[$columnAlias] = $columnExpression;
            }
        }

        $infoQuery = $this->getMainQuery()
            ->select($select)
            ->innerJoin($alias, "$infoAlias.user_id = $alias.user_id AND $infoAlias.site_id = :siteId", ['siteId' => $siteId]);

        $query
            ->leftJoin([$infoAlias => $infoQuery], "$infoAlias.user_id = $alias.user_id");
    }
}
