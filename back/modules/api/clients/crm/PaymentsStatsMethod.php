<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\UserTransaction;
use app\back\modules\api\ApiGetMethod;
use app\back\modules\api\components\Operators;
use app\back\modules\api\params\ApiParamSiteId;
use app\back\repositories\Refcodes;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class PaymentsStatsMethod extends ApiGetMethod
{
    use ApiParamSiteId;

    #[BigIdValidator]
    #[Operators(Operators::EQ_IN)]
    protected array $user_id = [];

    #[Operators(Operators::COMPARISONS)]
    #[DateTimeValidator]
    protected array $payed_at = [];

    #[Operators(Operators::PREFIX_IN, Operators::PREFIX_NOT_IN)]
    #[StringValidator(2, 250)]
    protected array $pay_refcode = [];

    #[Operators(Operators::EQ)]
    #[StringInArrayValidator(['day', 'week', 'month'], true)]
    protected array $date_group = [];

    public function run(): iterable
    {
        $request = $this->createRequest();

        $dateGroup = $this->db->getQuoter()->quoteValue($request->getParam('date_group', 'day'));
        $dateExpr = "date_trunc({$dateGroup}, us.updated_at)::date";

        $refcodeExpr = Refcodes::cleanCodeExpression();

        $query = (new Query($this->db))
            ->select([
                'pay_refcode' => $refcodeExpr,
                'payed_at' => $dateExpr,
                'payments_count' => 'COUNT(*)',
                'users_count' => 'COUNT(DISTINCT(us.site_id, us.user_id))',
                'sum_eur' => 'SUM(us.amount_eur)',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = us.refcode_id')
            ->where([
                'us.status' => UserTransaction::STATUS_SUCCESS,
                'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                'us.dir' => UserTransaction::DIR_IN,
            ])
            ->groupBy([$refcodeExpr, $dateExpr]);

        $request->filterLargeIn($this->db, $query, 'user_id', [
            'us.user_id' => 'intval',
        ]);

        $request->map([
            'payed_at' => 'us.updated_at',
            'site_id' => 'us.site_id',
            'pay_refcode' => $refcodeExpr,
        ], true);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }
}
