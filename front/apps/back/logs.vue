<template>
    <card width="wide">
        <rich-table
            v-bind="richTable"
            :showPagination="true"
            :showTotal="false"
            :reloadOnChange="false"
            sticky
            @reload="onReload"
            @change="onChange"
        >
            <template #afterTitle>
                <button
                    class="btn btn-sm btn-info ms-2"
                    type="button"
                    @click="onDownload"
                >
                    <icona name="icn-download" />
                    CSV
                </button>
            </template>

            <template #params="{row}">
                {{ row.params }}
                <template v-if="row.full_url">
                    <br>
                    <a
                        :href="row.full_url"
                        target="_blank"
                    >preview in new tab</a>
                </template>
            </template>
        </rich-table>
    </card>
</template>

<script lang="ts">

import { Icona, RichTable } from '@/components'
import { download } from '@/utils'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, Values } from '@/types'

export default defineComponent({
    components: {
        Icona,
        Card,
        RichTable,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {
                form: {} as FormGridType,
            },
        }
    },

    methods: {
        async reload (params: Values) {
            this.richTable.form = await this.$fetch(this.$route.path + '/form', params)
        },
        onChange (params: Values) {
            this.richTable.form.values = params
            this.$historyReplaceParams(params)
        },
        onReload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        onDownload () {
            this.$fetch(this.$route.path + '/download', this.richTable.form.values).then((csv: string) => {
                download(csv, 'employees.csv')
            })
        },
    },
})
</script>
