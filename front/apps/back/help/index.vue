<template>
    <rich-table
        v-bind="richTable"
        show-pagination
        @reload="onReload"
    >
        <template #elements="{row}">
            <span
                v-for="element in row.elements.slice(0,5)"
                class="badge bg-secondary me-1"
            >
                {{ element }}
            </span>
            <span
                v-if="row.elements.length > 5"
                class="badge bg-secondary me-1"
            >
                ...
            </span>
        </template>
        <template #actions="{row, refreshCallback}">
            <div class="btn-group btn-group-xs">
                <router-link
                    class="btn btn-warning"
                    :to="{name: 'help-update', query: {url: row.url}}"
                >
                    <icona name="icn-pencil" /> Edit page
                </router-link>
                <button
                    type="button"
                    class="btn btn-danger"
                    @click="onDelete(row, refreshCallback)"
                >
                    <icona name="icn-delete" /> Delete
                </button>
            </div>
        </template>
    </rich-table>
</template>

<script lang="ts">
import { Icona, RichTable } from '@/components'
import { defineComponent } from 'vue'
import { NextWithReload, Values, TableRow } from '@/types'

export default defineComponent({
    components: {
        Icona,
        RichTable,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {},
        }
    },

    methods: {
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        onDelete (row: TableRow, refreshCallback: () => void) {
            if (confirm('Are you sure?')) {
                this.$fetch(this.$route.path + '/delete', { id: row.id }).then(refreshCallback)
            }
        },
    },
})
</script>
