<template>
    <RichTable
        v-bind="richTableProps"
        @reload="onReload"
    >
        <template #afterTitle>
            <template v-if="!bonusResponse.userErrors">
                <Popover
                    :isOpened="remoteBonusType !== null"
                    :onClick="false"
                    :title="form.popoverTitle"
                    position="bottom"
                    @open="showBonusForm"
                    @close="remoteBonusType = null"
                >
                    <button
                        v-for="button in bonusResponse.addBonusButtons"
                        class="btn btn-sm btn-success me-2"
                        type="button"
                        @click="onBonusClick(button.remoteBonusType)"
                    >
                        {{ button.title }}
                    </button>
                    <template #content>
                        <FormGrid
                            v-bind="form"
                            style="min-width: 500px;"
                            @change="onChange"
                            @submit="onSubmit"
                        />
                    </template>
                </Popover>
            </template>
            <template v-else>
                <span class="badge bg-danger"> {{ bonusResponse.userErrors }}</span>
            </template>
        </template>
    </RichTable>
</template>

<script lang="ts">

import { FormGrid, Popover } from '@/components'
import { _, uuid4 } from '@/utils'
import { defineComponent } from 'vue'
import { FormGridType, RichTableType, Values } from '@/types'
import CommonTable from './common-table.vue'

interface BonusResponse extends RichTableType {
    userErrors: string | null
    addBonusButtons: {
        remoteBonusType: string
        title: string
    }[]
}

export default defineComponent({
    components: {
        Popover,
        FormGrid,
    },
    mixins: [
        CommonTable,
    ],
    data () {
        return {
            remoteBonusType: null as string | null,
            form: {} as FormGridType & { popoverTitle?: string },
            idempotenceId: null as string | null,
            bonusResponse: {} as BonusResponse,
        }
    },

    watch: {
        'form.values': {
            handler () {
                this.idempotenceId = uuid4()
            },
            immediate: true,
        },
        loadPromise: {
            immediate: true,
            async handler (loadPromise: Promise<BonusResponse>) {
                const data = await loadPromise
                this.bonusResponse.userErrors = data.userErrors
                this.bonusResponse.addBonusButtons = data.addBonusButtons
            },
        },
    },

    methods: {
        onBonusClick (remoteBonusType: string) {
            this.remoteBonusType = this.remoteBonusType !== null ? null : remoteBonusType
        },
        async showBonusForm () {
            this.form = {}
            this.form = await this.$fetch('/user/player/bonuses/add-form', {
                ...this.siteIdUserId,
                remoteBonusType: this.remoteBonusType,
            }).catch(() => {
                this.remoteBonusType = null
            })
        },
        onChange (values: Values) {
            if (!_.isEqual(values.games, this.form.values?.games)) {
                values.idempotenceId = this.idempotenceId
                return this.$processFormResponse<FormGridType>(this.$fetch('/user/player/bonuses/fill-form', {
                    ...this.siteIdUserId,
                    remoteBonusType: this.remoteBonusType,
                    values,
                }), this.form).then(formData => {
                    this.$setForm(this.form, formData)
                })
            }
            this.form.values = values
        },
        onSubmit (values: Values) {
            this.$processFormResponse(this.$fetch('/user/player/bonuses/add', {
                ...this.siteIdUserId,
                remoteBonusType: this.remoteBonusType,
                values: {
                    ...values,
                    idempotenceId: this.idempotenceId,
                },
            }), this.form).then(() => {
                this.remoteBonusType = null
            })
        },
    },
})
</script>
