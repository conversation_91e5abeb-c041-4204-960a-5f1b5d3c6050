<template>
    <ul>
        <li v-for="(item, index) in listAsArray" :key="index">
            <strong v-if="item[0] !== null">{{ camelToWords(item[0]) }}: </strong>
            <KeyValueList
                v-if="typeof item[1] === 'object' && item[1] !== null"
                :items="item[1]"
                :priorityKeyName="priorityKeyName"
            />
            <template v-else>
                {{ item[1] }}
            </template>
        </li>
    </ul>
</template>

<script lang="ts" setup>
import { ScalarOrEmpty } from '@/types.ts'
import { computed } from 'vue'

type Items = ScalarOrEmpty[] | Record<string, ScalarOrEmpty>
const $props = withDefaults(defineProps<{
    items: Items
    priorityKeyName?: string
}>(), {
    priorityKeyName: undefined,
})

const listAsArray = computed<[string | null, ScalarOrEmpty | Items][]>(() => {
    if (Array.isArray($props.items)) {
        return $props.items.map(val => {
            let key = null
            if ($props.priorityKeyName && typeof val === 'object' && val !== null && $props.priorityKeyName in val) {
                key = val[$props.priorityKeyName]
            }
            return [key, val]
        })
    }

    return Object.entries($props.items).filter(([key]) => !($props.priorityKeyName && key === $props.priorityKeyName));
})

function camelToWords(key: string) {
    return key
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .replace(/_/g, ' ');
}
</script>
