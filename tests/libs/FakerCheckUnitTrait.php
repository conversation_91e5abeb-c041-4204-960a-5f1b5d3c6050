<?php

declare(strict_types=1);

namespace app\tests\libs;

use app\back\components\helpers\Json;
use app\back\config\tasks\Res;
use app\back\entities\BettingBet;
use app\back\entities\Check;
use app\back\entities\Rate;
use app\back\modules\checks\checks\filters\BaseFilter;
use app\back\modules\checks\checks\FiltersFactory;
use app\back\modules\checks\checks\rules\BaseRule;
use app\back\modules\checks\checks\RulesFactory;
use app\back\repositories\BettingBets;
use app\back\repositories\CheckGroups;
use app\back\repositories\Checks;

trait FakerCheckUnitTrait
{
    protected function haveCheckRecord(array $customAttributes, ?int $departmentId = null): Check
    {
        $customAttributes['rules'] ??= [];
        $customAttributes['filters'] ??= [];
        array_walk($customAttributes['rules'], static fn(array | BaseRule &$r) => $r = $r instanceof BaseRule ? get_object_vars($r) : $r);

        $filterManager = $this->container()->get(FiltersFactory::class);
        $rulesManager = $this->container()->get(RulesFactory::class);

        self::assertFilterOrRuleIsValid($filterManager->create($customAttributes['source']), $customAttributes['filters']);
        array_walk(
            $customAttributes['rules'],
            static fn(array $rule) => self::assertFilterOrRuleIsValid($rulesManager->createEmpty($rule['id']), $rule)
        );

        if ($departmentId !== null) {
            /** @var CheckGroups $checkGroupRecord */
            $checkGroupRecord = $this->haveRecord(CheckGroups::class, [
                "name" =>  uniqid('Some group name', true),
                "department_id" => $departmentId,
            ]);
        }
        $checkGroupId = $checkGroupRecord->id ?? $this->haveRecord(CheckGroups::class, [
            "name" => uniqid('Some group name', true),
        ])->id;

        $checkRecord = $this->haveRecord(Checks::class, array_merge([
            "group_id" => $checkGroupId,
            "active" => true,
            "operator" => Check::OPERATOR_AND,
            "score" => 1000,
        ], $customAttributes));

        self::assertInstanceOf(Check::class, $checkRecord);

        return $checkRecord;
    }

    protected function haveBettingBetRecord(array $customAttributes = []): BettingBet
    {
        $currencyOrig = $customAttributes['currency'] ?? self::randomCurrency();
        $refundOrig = $customAttributes['refund'] ?? self::randomMoney();
        $stakeOrig = $customAttributes['stake'] ?? self::randomMoney();
        $date = $customAttributes['created_at'] ?? new \DateTimeImmutable();
        $dateRate = $date->format('Y-m-d H:i:s');

        $bettingBetRecord = $this->haveRecord(BettingBets::class, [
            'id' => uniqid('', false),
            'type' => $customAttributes['type'] ?? self::randomItem(array_keys(BettingBet::TYPES)),
            'status' => $customAttributes['status'] ?? self::randomItem(array_keys(BettingBet::STATUSES)),
            'site_id' => $customAttributes['site_id'] ?? self::uniqSiteId(),
            'user_id' => $customAttributes['user_id'] ?? self::uniqRuntimeId(),
            'created_at' => $date,
            'updated_at' => $customAttributes['updated_at'] ?? $date,
            'currency' => $currencyOrig,
            'refund' => $refundOrig,
            'refund_usd' => (string) ($customAttributes['refund_usd'] ?? $this->ratesRepo()->convert($refundOrig, $currencyOrig, Rate::USD, $dateRate)),
            'refund_eur' => (string) ($customAttributes['refund_eur'] ?? $this->ratesRepo()->convert($refundOrig, $currencyOrig, Rate::RUB, $dateRate)),
            'refund_rub' => (string) ($customAttributes['refund_rub'] ?? $this->ratesRepo()->convert($refundOrig, $currencyOrig, Rate::EUR, $dateRate)),
            'stake' => $stakeOrig,
            'stake_usd' => (string) ($customAttributes['stake_usd'] ?? $this->ratesRepo()->convert($stakeOrig, $currencyOrig, Rate::USD, $dateRate)),
            'stake_eur' => (string) ($customAttributes['stake_eur'] ?? $this->ratesRepo()->convert($stakeOrig, $currencyOrig, Rate::RUB, $dateRate)),
            'stake_rub' => (string) ($customAttributes['stake_rub'] ?? $this->ratesRepo()->convert($stakeOrig, $currencyOrig, Rate::EUR, $dateRate)),
        ]);
        self::assertInstanceOf(BettingBet::class, $bettingBetRecord);
        return $bettingBetRecord;
    }

    protected function randomResourceWithSiteId(string ...$platforms): array
    {
        if (count($platforms) === 0) {
            $platforms = [Res::PLATFORM_SMEN, Res::PLATFORM_GI, Res::PLATFORM_YS];
        }
        $resources = array_merge(...array_map(static fn($p) => Res::PLATFORMS_RESOURCES[$p], $platforms));
        $resource = $resources[array_rand($resources)];
        return [$resource, Res::RESOURCE_TO_SITE[$resource]];
    }

    protected static function assertFilterOrRuleIsValid(BaseFilter|BaseRule $filterOrRule, array $data): void
    {
        $validationErrors = $filterOrRule->validate($data);
        self::assertEmpty($validationErrors, $filterOrRule::class . "\n" . Json::encode($validationErrors));
    }

    private function timeShift(string $time, string $shift): string
    {
        return date('Y-m-d H:i:s', strtotime($shift, strtotime($time)));
    }
}
