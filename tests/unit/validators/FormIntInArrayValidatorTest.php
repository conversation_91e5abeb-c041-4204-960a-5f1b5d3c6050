<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\Form;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringValidator;
use PHPUnit\Framework\TestCase;

class FormIntInArrayValidatorTest extends TestCase
{
    public function testPredefined(): void
    {
        $form = new class
        {
            use Form;

            #[IntInArrayValidator([10, 20, 30], true)]
            public int $i;
        };

        $errors = $form->validate(['i' => '11']);
        $this->assertSame(['i' => 'I 11 is not allowed'], $errors);

        $errors = $form->validate(['i' => [10]]);
        $this->assertSame(['i' => 'I must be scalar'], $errors);

        $errors = $form->validate(['i' => '10']);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 10], $form->params());

        $errors = $form->validate(['i' => 20]);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 20], $form->params());
    }

    public function testKeys(): void
    {
        $form = new class
        {
            use Form;

            #[IntInArrayValidator([5 => 1, 10 => 2])]
            public int $i;
        };

        $errors = $form->validate(['i' => 2]);
        $this->assertSame(['i' => 'I 2 is not allowed'], $errors);

        $errors = $form->validate(['i' => [1]]);
        $this->assertSame(['i' => 'I must be scalar'], $errors);

        $errors = $form->validate(['i' => '10']);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 10], $form->params());

        $errors = $form->validate(['i' => 5]);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 5], $form->params());
    }

    public function testCallback(): void
    {
        $form = new class
        {
            use Form;

            #[IntInArrayValidator([self::class, 'allowedArrayKeys'])]
            public int $i;

            public static function allowedArrayKeys(): array
            {
                return [10 => 'qwe', 20 => 'asd'];
            }
        };

        $errors = $form->validate(['i' => 2, 'w' => 'z']);
        $this->assertSame(['i' => 'I 2 is not allowed'], $errors);

        $errors = $form->validate(['i' => [1]]);
        $this->assertSame(['i' => 'I must be scalar'], $errors);

        $errors = $form->validate(['i' => '10']);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 10], $form->params());

        $errors = $form->validate(['i' => 20]);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 20], $form->params());
    }

    public function testCallbackContext(): void
    {
        $form = new class
        {
            use Form;

            #[IntInArrayValidator([FormIntInArrayValidatorTest::class, 'allowedArrayKeys'])] // Invalid inspection
            public int $i;

            public int $w;
            #[StringValidator]
            public string $c;

            public array $availableArr = [10 => 'qwe', 20 => 'asd'];
        };

        $errors = $form->validate(['i' => '10', 'w' => 'z', 'c' => 'some string']);
        $this->assertSame([], $errors);
        $this->assertSame(['i' => 10, 'c' => 'some string'], $form->params());
    }

    public static function allowedArrayKeys($form, array $context): array
    {
        self::assertSame(['i' => '10', 'w' => 'z', 'c' => 'some string'], $context);

        return $form->availableArr;
    }
}
