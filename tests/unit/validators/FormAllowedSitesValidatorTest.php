<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\AllowedLists;
use app\back\components\Form;
use app\back\components\validators\AllowedSitesValidator;
use app\tests\libs\mock\FakeAllowedListsTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

class FormAllowedSitesValidatorTest extends TestCase
{
    use FakeAllowedListsTrait;
    use DbTransactionalUnitTrait;

    public function testBasic(): void
    {
        $form = new class ($this->fakeAllowedSites([7 => 'qwe', 10 => 'asd']))
        {
            use Form;

            #[AllowedSitesValidator]
            public array $arr;

            public function __construct(
                public AllowedLists $allowedLists
            ) {
            }
        };

        $errors = $form->validate([]);
        $this->assertSame(['arr' => 'Arr is required'], $errors);

        $errors = $form->validate(['arr' => null]);
        $this->assertSame(['arr' => 'Arr is required'], $errors);

        $errors = $form->validate(['arr' => 123]);
        $this->assertSame(['arr' => 'Arr is not array'], $errors);

        $errors = $form->validate(['arr' => ['qwe']]);
        $this->assertSame(['arr' => 'Arr qwe is not allowed at 0'], $errors);

        $errors = $form->validate(['arr' => [7, 5]]);
        $this->assertSame(['arr' => 'Arr 5 is not allowed at 1'], $errors);

        $errors = $form->validate(['arr' => []]);
        $this->assertSame([], $errors);
        $this->assertSame(['arr' => []], $form->params());

        $errors = $form->validate(['arr' => [7]]);
        $this->assertSame([], $errors);
        $this->assertSame(['arr' => [7]], $form->params());

        $errors = $form->validate(['arr' => [7, '10']]);
        $this->assertSame([], $errors);
        $this->assertSame(['arr' => [7, 10]], $form->params());

        $errors = $form->validate(['arr' => ['zxc' => 7]]);
        $this->assertSame([], $errors);
        $this->assertSame(['arr' => ['zxc' => 7]], $form->params());
    }

    public function testNotEmpty(): void
    {
        $form = new class ($this->fakeAllowedSites([7 => 'qwe', 10 => 'asd'])) {
            use Form;

            #[AllowedSitesValidator(false)]
            public array $arr;

            public function __construct(
                public AllowedLists $allowedLists
            ) {
            }
        };

        $errors = $form->validate(['arr' => []]);
        $this->assertSame(['arr' => 'Arr is empty'], $errors);
    }
}
