<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\Form;
use app\back\components\validators\MoneyValidator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class FormMoneyValidatorTest extends TestCase
{
    #[DataProvider('dataBasic')]
    public function testBasic(mixed $testValue, array $expected): void
    {
        $form = new class
        {
            use Form;

            #[MoneyValidator]
            public mixed $value;
        };
        $error = $form->validate(['value' => $testValue]);
        $this->assertSame($expected, $error);
    }

    public static function dataBasic(): array
    {
        return [
            ['223.00', []],
            ['100', []],
            [2321.89898, []],
            ['1E10', []],
            ['', []],
            [0, []],
            ['10000000000000000000', ['value' => 'Value is too big (10000000000000000000.00 > 9223372036854775807)']],
            [-10, ['value' => 'Value is too small (-10.00 < 0)']],
            ['332,22', ['value' => 'Value is not numeric']],
            ['invalid', ['value' => 'Value is not numeric']],
            ['i12345', ['value' => 'Value is not numeric']],
            [[], ['value' => 'Value is not numeric']],
            [new \stdClass(), ['value' => 'Value is not numeric']],
        ];
    }
}
