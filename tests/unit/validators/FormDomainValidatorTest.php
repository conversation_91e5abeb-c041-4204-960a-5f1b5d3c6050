<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\Form;
use app\back\components\validators\DomainValidator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

#[CoversClass(DomainValidator::class)]
class FormDomainValidatorTest extends TestCase
{
    #[DataProvider('dataBasic')]
    public function testBasic(mixed $testValue, array $expected): void
    {
        $form = new class
        {
            use Form;

            #[DomainValidator]
            public string $value;
        };

        $error = $form->validate(['value' => $testValue]);
        $this->assertSame($expected, $error);
    }

    #[DataProvider('dataLength')]
    public function testDomainLength(mixed $testValue, array $expected): void
    {
        $form = new class
        {
            use Form;

            #[DomainValidator(20)]
            public mixed $value;
        };
        $error = $form->validate(['value' => $testValue]);
        $this->assertSame($expected, $error);
    }

    public static function dataBasic(): array
    {
        return [
            ['t.me', []],
            ['example.com', []],
            ['укр.нет', []],
            ['bücher.ch', []],
            ['example.', []],
            ['xn--bcher-kva.ch', []],
            ['invalid_domain', ['value' => 'Value is invalid domain']],
            ['<EMAIL>', ['value' => 'Value is invalid domain']],
            ['-example.com', ['value' => 'Value is invalid domain']],
            ['example-.com', ['value' => 'Value is invalid domain']],
            ['example..com', ['value' => 'Value is invalid domain']],
        ];
    }

    public static function dataLength(): array
    {
        return [
            ['01234567890123456.com', ['value' => 'Value is too long (21 > 20)']],
            ['десять.укр', ['value' => 'Value is too long (24 > 20)']],
            ['t.c', ['value' => 'Value is too short (3 < 4)']],
        ];
    }
}
