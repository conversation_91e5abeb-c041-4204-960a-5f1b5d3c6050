<?php

declare(strict_types=1);

namespace app\tests\unit\modules\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\modules\checks\checks\rules\UserInfoRule;
use app\back\repositories\UserSpecialInfos;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use PHPUnit\Framework\TestCase;

class UserInfoRuleTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;
    use CheckRulesTrait;

    public function testLifetimeDepositCount(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();
        $pk = "$siteId-$userId";
        $rule = $this->container()->get(UserInfoRule::class);

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'dep_lt_count' => 0,
        ]);

        Arr::configure($rule, ['metric' => UserInfoRule::METRIC_DEP_COUNT, 'value' => 1, 'valueOperator' => '=', 'valueUnit' => 'number']);
        $this->assertCheckResult($rule, $pk, false, 'Lifetime deposit count is 0');

        $this->db()->createCommand()->update(UserSpecialInfos::TABLE_NAME, [
            'dep_lt_count' => 2, 'dep_last_at' => DateHelper::daysAgo(10)])->execute();
        $this->assertCheckResult($rule, $pk, false, 'Lifetime deposit count is 2');

        Arr::configure($rule, ['valueOperator' => '>']);
        $this->assertCheckResult($rule, $pk, true, 'Lifetime deposit count is 2');

        $randomPk = self::uniqSiteId() . '-' . self::uniqRuntimeId();
        $this->assertCheckResult($rule, $randomPk, null);
    }

    public function testLastDepositDate(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();
        $pk = "$siteId-$userId";
        $rule = $this->container()->get(UserInfoRule::class);

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'dep_last_at' => null,
        ]);

        Arr::configure($rule, ['value' => 2, 'valueOperator' => '>', 'valueUnit' => 'day', 'metric' => UserInfoRule::METRIC_LD_DATE]);
        $this->assertCheckResult($rule, $pk, null);

        $this->db()->createCommand()
            ->update(UserSpecialInfos::TABLE_NAME, ['dep_last_at' => DateHelper::daysAgo(3)])
            ->execute();
        $this->assertCheckResult($rule, $pk, true, 'Last dep was 3 days ago');

        Arr::configure($rule, ['value' => 2, 'valueOperator' => '<', 'valueUnit' => 'day']);
        $this->assertCheckResult($rule, $pk, false, 'Last dep was 3 days ago');

        $this->db()->createCommand()
            ->update(UserSpecialInfos::TABLE_NAME, ['dep_last_at' => DateHelper::daysAgo(8)])
            ->execute();

        Arr::configure($rule, ['value' => 1, 'valueOperator' => '<', 'valueUnit' => 'week']);
        $this->assertCheckResult($rule, $pk, false, 'Last dep was 8 days ago');

        Arr::configure($rule, ['value' => 1, 'valueOperator' => '>', 'valueUnit' => 'week']);
        $this->assertCheckResult($rule, $pk, true, 'Last dep was 8 days ago');

        $date = date('Y-m-d H:i:s', strtotime('- 2 hour'));
        $time = date('H:i:s', strtotime($date));

        $this->db()->createCommand()
            ->update(UserSpecialInfos::TABLE_NAME, ['dep_last_at' => $date])
            ->execute();
        Arr::configure($rule, ['value' => 1, 'valueOperator' => '>', 'valueUnit' => 'day']);
        $this->assertCheckResult($rule, $pk, false, "Last dep was -$time ago");

        Arr::configure($rule, ['value' => 1, 'valueOperator' => '<', 'valueUnit' => 'day']);
        $this->assertCheckResult($rule, $pk, true, "Last dep was -$time ago");
    }
}
