<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\AffParam;
use app\back\entities\Site;
use app\back\entities\UserLyraActivity;
use app\back\repositories\AffParams;
use app\back\repositories\Games;
use app\back\repositories\GameSources;
use app\back\repositories\Rates;
use app\back\repositories\UserGameRaws;
use app\back\repositories\UserLogins;
use app\back\repositories\UserLyraActivities;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;

class UsersLyraActivitySourceTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testUsers(): void
    {
        $this->dontSeeRecord(UserLyraActivities::class, ['site_id' => Site::CV, 'user_id' => 78552510]);

        $this->runTask('users', Res::CV, $this->csv($this->usersCsv()));

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::CV,
                'user_id' => 78552510
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_USERS,
                'source_updated_at' => new \DateTimeImmutable('2023-05-05 18:51:10'),
                'update_count' => 1
            ]
        );
    }

    public function testUsersStats(): void
    {
        $this->fillCurrencyRates();

        $this->dontSeeRecord(UserLyraActivities::class, ['site_id' => Site::CV, 'user_id' => ********]);

        $this->runTask('users-stats', Res::CV, '-1 minute', 'PT1M', $this->csv([[
            'id' => '509346292',
            'userId' => '********',
            'type' => '',
            'opId' => '11',
            'status' => '3',
            'sum_usd' => '6.6306',
            'sum' => '593.5000',
            'sum_locked' => '',
            'currency' => 'USD',
            'paySys' => 'inner_pay',
            'requisite' => '',
            'dateCreated' => '2023-07-10 00:00:00',
            'dateSent' => '',
            'dateUpdated' => '2023-07-10 00:00:00',
            'balanceAfterChanges' => '3393.9400',
            'comment' => 'Обмен баллов',
            'commentPayment' => '',
            'platform' => '',
            'externalPaySystem' => 'innerpay',
            'processTransactionId' => 'd7797d7d-3806-497e-89e4-1ee1b577b1e5',
            'accountId' => '',
            'useragent' => '',
            'refcode' => '',
            'ip' => '',
            'domain' => '',
            'balanceType' => 'real',
        ]]));

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::CV,
                'user_id' => ********
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_TRANSACTIONS,
                'source_updated_at' => new \DateTimeImmutable('2023-07-10 00:00:00'),
                'update_count' => 1
            ]
        );
    }

    public function testUsersLogins(): void
    {
        $this->dontSeeRecord(UserLyraActivities::class, ['site_id' => Site::CV, 'user_id' => 4080428]);


        $this->runTask('users-logins', Res::CV, $this->csv([[
            'userId' => '4080428',
            'date' => '2023-07-10 00:00:05',
            'ip' => '*************',
            'success' => '1',
            'failReason' => '',
            'userAgent' => 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
            'affData' => '',
            'domain' => 'king-vulkan-club.com',
            'bulkId' => '',
            'xRequestedWith' => '3d22ccd1-5f30-4042-bf80-7455050a2633',
            'loginMethod' => 'remember_me',
            'refCode' => 'seo|google.|-',
            'uuid' => '',

        ]]));

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::CV,
                'user_id' => 4080428
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_LOGINS,
                'source_updated_at' => new \DateTimeImmutable('2023-07-10 00:00:05'),
                'update_count' => 1
            ]
        );
    }

    public function testUsersGamesTokens(): void
    {
        $this->fillCurrencyRates();

        $this->db()->createCommand()->insert(
            GameSources::TABLE_NAME,
            ['game_id' => '876', 'source' => '3', 'name' => '7|3976', 'created_at' => '2023-06-08 08:24:18'],
        )->execute();

        $this->db()->createCommand()->insert(
            Games::TABLE_NAME,
            ['id' => 876, 'name' => '100 Zombies', 'platform' => 3, 'created_at' => '2023-06-08 08:24:18', 'type' => 10, 'vendor_id' => 276],
        )->execute();

        $this->dontSeeRecord(UserLyraActivities::class, ['site_id' => Site::CV, 'user_id' => 6972755]);


        $this->runTask('users-games-tokens', Res::CV, $this->csv([[
            '_id' => 't:6972755:e8c65cca-d297-42bb-a3b3-ed2c6a73f9e9',
            'balanceType' => 'real',
            'providerIntegrationName' => 'hhs_prod',
            'userId' => '6972755',
            'game' => '100_zombies',
            'gameId' => '3976',
            'createdAt' => '2023-07-10 00:03:17',
            'active' => '1',
            'currency' => 'RUB',
            'version' => '2',
            'lastLaunchAt' => '2023-07-10 00:03:42',
            'free' => 'false',
            'betAmount' => '700',
            'betCount' => '7',
            'winAmount' => '325',
            'winCount' => '3',
            'winMax' => '270',
            'clientIp' => '************',
            'clientUserAgent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 YaBrowser/23.7.0.2092.10 SA/3 Mobile/15E148 Safari/604.1',
            'refCode' => 'seo|yandex.|-',
            'domain' => 'elite-vulkan.club',
        ]]));

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::CV,
                'user_id' => 6972755
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_GAMES,
                'source_updated_at' => new \DateTimeImmutable('2023-07-10 00:03:42'),
                'update_count' => 1
            ]
        );
    }

    public function testUsersGamesAggregate(): void
    {
        $this->db()->createCommand()->batchInsert(
            UserGameRaws::TABLE_NAME,
            [
                'site_id',
                'spin_id',
                'user_id',
                'type',
                'auth_token',
                'session_token',
                'provider_id',
                'game_id',
                'round_type',
                'round_id',
                'action_id',
                'currency',
                'real_amount',
                'bonus_amount',
                'created_at',
                'real_amount_after',
                'bonus_amount_after'
            ],
            [
                [
                    '27', '0000000000fd2c4464811a040db3d07d09fa39b80001', '16591940', 'bet', '338e5c6dc2789a14c3b95c8fe95204dc', '3109c442-ac9b-4622-8224-456ef7d2e00a', 'endorphina', '1342', 'paid', 'bvulkanvegas__pb5p16591940__71b17666-dc04-4865-8013-6a106ae5858a-27', '6000002325105336', 'EUR', '5.00', '0.00', '2023-07-10 00:00:04', '415.02', '0.00'
                ],
                [
                    '27', '0000000000fd2c4464811a0304a7688ddfc32c6f0001', '16591940', 'bet', '338e5c6dc2789a14c3b95c8fe95204dc', '3109c442-ac9b-4622-8224-456ef7d2e00a', 'endorphina', '1342', 'paid', 'bvulkanvegas__pb5p16591940__71b17666-dc04-4865-8013-6a106ae5858a-26', '6000002325105054', 'EUR', '5.00', '0.00', '2023-07-10 00:00:03', '420.02', '0.00'
                ]
            ]
        )->execute();


        $this->fillCurrencyRates();

        $this->dontSeeRecord(UserLyraActivities::class, ['site_id' => Site::VV, 'user_id' => 16591940]);

        $this->runTask('update-users-games-aggregate', Res::VV, '2023-07-10');

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::VV,
                'user_id' => 16591940
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_GAMES,
                'source_updated_at' => new \DateTimeImmutable('2023-07-10 00:00:04'),
                'update_count' => 1
            ]
        );
    }

    public function testRokeenteSessions(): void
    {
        $debugCsv = $this->csv([[
            'session' => '87a5c3d30b6221b8737bcd7dcd3a6c39',
            'real_time' => '2023-07-09 23:59:51',
            'sub_1' => 'wp_w67371p232_',
            'project' => 'wp',
            'client_time' => '2023-07-10 07:59:48',
            'has_dev_tools_open' => 'false',
            'has_vektor_browser' => 'false',
            'has_incognito' => 'false',
            'is_iframe' => 'false',
            'has_apple_pay' => 'false',
            'browser_user_agent' => 'Mozilla/5.0 (Linux; Android 8.0.0; S55 Build/O00623; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/113.0.5672.77 Mobile Safari/537.36',
            'server_user_agent' => 'Mozilla/5.0 (Linux; Android 8.0.0; S55 Build/O00623; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/113.0.5672.77 Mobile Safari/537.36',
            'operation_system' => 'Linux armv8l',
            'app_agent' => '5.0 (Linux; Android 8.0.0; S55 Build/O00623; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/113.0.5672.77 Mobile Safari/537.36',
            'oscpu' => '',
            'app_code_name' => 'Mozilla',
            'do_not_track' => '',
            'hardware_concurrency' => '8',
            'max_touch_points' => '5',
            'language' => 'ru-RU',
            'languages' => 'ru-RU,en-US',
            'product_sub' => '20030107',
            'vendor' => 'Google Inc.',
            'screen_width' => '360',
            'screen_height' => '720',
            'width' => '360',
            'height' => '720',
            'color_depth' => '24-bit',
            'inner_width' => '360',
            'inner_height' => '696',
            'orientation_type' => 'portrait-primary',
            'unmasked_renderer' => 'Mali-T860',
            'unmasked_vendor' => 'ARM',
            'asn' => 'AS41330',
            'real_ip_address' => '**************',
            'hostname' => '',
            'proxy_types' => '',
            'time_zone' => 'Asia/Novosibirsk',
            'isp' => 'Tele2 Russia',
            'organization' => 'Tele2 Russia',
            'autonomous_system_organization' => 'T2 Mobile LLC',
            'registered_country' => 'Russia',
            'p0f_detected_os' => 'Linux 2.2.x-3.x [generic]',
            'p0f_network_link' => 'generic tunnel or VPN',
            'canvas_finger_print' => '2275D5A51D2A343BE7041A7EAFAAB909',
            'webgl_finger_print' => 'D4F1BD298AB73948C296F8FAA958A9EB',
            'http_referer' => '',
            'promo_engine_http_referrer' => '',
            'browser_plugins' => '',
            'x_requested_with' => 'stats.refguide.llcaap',
            'http_via' => '',
            'pixel_cookie' => '87a5c3d30b6221b8737bcd7dcd3a6c39',
            'proxies' => 'NO PROXY',
            'http_x_forwarded_for' => '',
            'referrer_android_app' => '',
            'fastfraud_screen_resolution_score' => '0.21',
            'updated_at' => '2023-07-10 18:51:10',
        ]]);


        $loginDate = '2022-09-01';
        $this->db()->createCommand()->insert(
            UserLogins::TABLE_NAME,
            [
                'site_id' => SITE::CV,
                'user_id' => '630746',
                'date' => $loginDate . ' 00:03:21.000000',
                'refcode_id' => '32',
                'success' => 'true',
                'fail_reason' => '',
                'ip' => '*************',
                'useragent_id' => '4567',
                'reg' => 'true',
                'aff_data_id' => '',
                'country' => 'RU',
                'host_id' => '24',
                'city_id' => '8',
                'sub_data_aff_param_id' => 1,
                'login_id' => '630746-2022-09-01 00:03:21'
            ],
        )->execute();

        $this->db()->createCommand()->insert(
            AffParams::TABLE_NAME,
            [
                'id' => '1',
                'param_id' => AffParam::PARAM_IDS[AffParam::P_SUBDATA],
                'value' => '87a5c3d30b6221b8737bcd7dcd3a6c39'
            ],
        )->execute();

        $this->dontSeeRecord(UserLyraActivities::class, ['site_id' => Site::CV, 'user_id' => 630746]);
        $this->runTask('update-rokeente-sessions-users', Res::CV, $loginDate);
        $this->runTask('rokeente-sessions', Res::ROK, $debugCsv);


//        $this->seeRecordWithFields(
//            UserLyraActivities::class,
//            [
//                'site_id' => Site::CV,
//                'user_id' => 630746
//            ],
//            [
//                'source_id' => UserLyraActivity::SOURCE_ROKEENTE_SESSIONS,
//                'source_updated_at' => new \DateTimeImmutable('2023-07-10 18:51:10'),
//                'update_count' => 1
//            ]
//        );
    }

    public function testUpdateSourceAndCounters(): void
    {
        $this->db()->createCommand()->insert(
            UserLyraActivities::TABLE_NAME,
            [
                'site_id' => Site::CV,
                'user_id' => 78552510,
                'source_id' => UserLyraActivity::SOURCE_LOGINS,
                'updated_at' => '2023-05-01 18:51:10',
                'source_updated_at' => '2023-05-01 18:51:10',
                'update_count' => 1
            ],
        )->execute();

        $this->fillCurrencyRates();

        $this->runTask('users', Res::CV, $this->csv($this->usersCsv()));

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::CV,
                'user_id' => 78552510
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_USERS,
                'source_updated_at' => new \DateTimeImmutable('2023-05-05 18:51:10'),
                'update_count' => 2
            ]
        );
    }

    public function testSkipUpdateWhenNewRowIsOlder(): void
    {
        $this->db()->createCommand()->insert(
            UserLyraActivities::TABLE_NAME,
            [
                'site_id' => Site::CV,
                'user_id' => 78552510,
                'source_id' => UserLyraActivity::SOURCE_LOGINS,
                'updated_at' => '2023-07-10 18:51:10',
                'source_updated_at' => '2023-07-10 18:51:10',
                'update_count' => 1
            ],
        )->execute();

        $this->fillCurrencyRates();

        $this->runTask('users', Res::CV, $this->csv($this->usersCsv()));

        $this->seeRecordWithFields(
            UserLyraActivities::class,
            [
                'site_id' => Site::CV,
                'user_id' => 78552510
            ],
            [
                'source_id' => UserLyraActivity::SOURCE_LOGINS,
                'updated_at' => new \DateTimeImmutable('2023-07-10 18:51:10'),
                'source_updated_at' => new \DateTimeImmutable('2023-07-10 18:51:10'),
                'update_count' => 1
            ]
        );
    }

    private function usersCsv(): array
    {
        return [[
            'id' => '78552510',
            'ip' => '2a00:1fa1:c20c:a70c:107f:6b8b:8389:4a4f',
            'email' => '<EMAIL>',
            'emailConfirmed' => '1',
            'phone' => '+79172658465',
            'login' => 'samigullin-timur',
            'social' => '',
            'locale' => 'ru',
            'birthday' => '1998-05-03',
            'gender' => 'm',
            'dateCreated' => '2023-05-05 18:51:10',
            'dateUpdated' => '2023-06-12 00:00:13',
            'dateLastLogin' => '2023-06-12 00:00:13',
            'dateEmailConfirmed' => '2023-05-20 06:40:32',
            'regType' => '',
            'refCode' => 'seo|yandex.|-',
            'isDeveloper' => '0',
            'isLocked' => '0',
            'walletId' => '78558087',
            'enabled' => '1',
            'isToxic' => '0',
            'withdrawalsDisabled' => '0',
            'brandId' => '1',
            'brandName' => 'ClubVulkan',
            'datePasswordRequested' => '',
            'registrationMethod' => 'email',
            'phoneConfirmed' => '0',
            'phoneConfirmedAt' => '',
            'uuid' => '6d72e532-cb2b-4693-9944-f80c83520384',
            'userAgent' => 'Dalvik/2.1.0 (Linux; U; Android 13; 2109119DG Build/TKQ1.220829.002) NativeApp/Android/com.adm777.app/v0.8.7/intbrowsrm',
            'registrationDomain' => 'club-vulkan.day',
            'landingPageUrl' => '/?ysclid=lhawvrvl9e482479099',
            'countryCode' => '',
            'city' => '',
            'street' => '',
            'zipCode' => '',
            'lastName' => '',
            'firstName' => '',
        ]];
    }

    private function fillCurrencyRates(): void
    {
        $this->db()->createCommand()->batchInsert(
            Rates::TABLE_NAME,
            ['code', 'date', 'rate'],
            [
                [
                    'RUB',
                    '2023-07-10 00:00:00',
                    75.1499940000
                ],
                [
                    'EUR',
                    '2023-07-10 00:00:00',
                    0.8869970000
                ]
            ]
        )->execute();
    }
}
