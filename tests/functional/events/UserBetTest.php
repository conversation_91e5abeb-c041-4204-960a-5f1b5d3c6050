<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\BettingLog;
use app\back\entities\Site;
use app\back\modules\events\events\UserBetEvent;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserBetEvent::class)]
class UserBetTest extends BaseEventTestCase
{
    public function testDecline(): void
    {
        $subscription = $this->haveSubscription('crm', 'users_bet', ['site_id' => Site::GGB, 'type' => BettingLog::TYPE_DECLINE]);

        $bettingLogRow = [
            'action_id' => uniqid('', false),
            'player_id' => self::uniqRuntimeId(),
            'auth_token' => uniqid('', false),
            'bet_id' => uniqid('', false),
            'bet_type' => 'paid',
            'action_type' => 'decline_bet',
            'currency' => self::randomCurrency(),
            'real_amount' => '0',
            'bonus_amount' => '0',
            'created_at' => date('Y-m-d\TH:i:s.vP'),
        ];

        MockServer::with(function () use ($bettingLogRow) {
            $this->runTask('betting-log', Res::GGB, self::LAST_MINUTE_PERIOD, $this->csv([
                $bettingLogRow
            ]));

            $this->sendQueuedEvents();
        }, [
            $this->eventRequest($subscription, [[
                'site_id' => Site::GGB,
                'user_id' => $bettingLogRow['player_id'],
                'bet_id' => $bettingLogRow['bet_id'],
            ]])
        ]);
    }
}
