<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\BettingSport;
use app\back\repositories\BettingSports;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

class CrmGetBettingTournamentsTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testGetTournamentsBySportID(): void
    {
        $date = date('Y-m-d H:i:s');
        $bettingTournament = $this->haveBettingTournamentRecord(['date' => $date]);
        /** @var BettingSport $bettingSport */
        $bettingSport = $this->haveRecord(BettingSports::class, [
            'id' => $bettingTournament->sport_id,
            'code' => (string) self::uniqRuntimeId(),
            'name' => (string) self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable(),
        ]);

        $this->sendAPI('crm', 'get/betting-tournaments', [
            'sport_id' => ['=' => $bettingTournament->sport_id],
        ]);

        $this->seeRowsInCsv([[
            'id' => $bettingTournament->id,
            'name' => $bettingTournament->name,
            'sport' => $bettingSport->name,
            'country' => $bettingTournament->country_code,
            'start_at' => $date,
            'end_at' => $date,
        ]]);
    }

    public function testGetTournamentsByCountry(): void
    {
        $date = date('Y-m-d H:i:s');
        $bettingTournament = $this->haveBettingTournamentRecord(['date' => $date]);

        /** @var BettingSport $bettingSport */
        $bettingSport = $this->haveRecord(BettingSports::class, [
            'id' => $bettingTournament->sport_id,
            'code' => (string) self::uniqRuntimeId(),
            'name' => (string) self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable(),
        ]);

        $this->sendAPI('crm', 'get/betting-tournaments', [
            'country' => ['=' => 'NL'],
        ]);

        $this->seeRowsInCsv([[
            'id' => $bettingTournament->id,
            'name' => $bettingTournament->name,
            'sport' => $bettingSport->name,
            'country' => $bettingTournament->country_code,
            'start_at' => $date,
            'end_at' => $date,
        ]]);
    }


    public function testGetTournamentsByStartAt(): void
    {
        $date = date('Y-m-d H:i:s');
        $dateFrom = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $dateTo = date('Y-m-d H:i:s', strtotime('+1 hour'));
        $bettingTournament = $this->haveBettingTournamentRecord(['date' => $date]);
        /** @var BettingSport $bettingSport */
        $bettingSport = $this->haveRecord(BettingSports::class, [
            'id' => $bettingTournament->sport_id,
            'code' => (string) self::uniqRuntimeId(),
            'name' => (string) self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable(),
        ]);

        $this->sendAPI('crm', 'get/betting-tournaments', [
            'start_at' => ['>' => $dateFrom, '<' => $dateTo],
        ]);

        $this->seeRowsInCsv([[
            'id' => $bettingTournament->id,
            'name' => $bettingTournament->name,
            'sport' => $bettingSport->name,
            'country' => $bettingTournament->country_code,
            'start_at' => $date,
            'end_at' => $date,
        ]]);
    }

    public function testGetTournamentsByEndAt(): void
    {
        $date = date('Y-m-d H:i:s');
        $dateFrom = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $dateTo = date('Y-m-d H:i:s', strtotime('+1 hour'));
        $bettingTournament = $this->haveBettingTournamentRecord(['date' => $date]);
        /** @var BettingSport $bettingSport */
        $bettingSport = $this->haveRecord(BettingSports::class, [
            'id' => $bettingTournament->sport_id,
            'code' => (string) self::uniqRuntimeId(),
            'name' => (string) self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable(),
        ]);

        $this->sendAPI('crm', 'get/betting-tournaments', [
            'end_at' => ['>' => $dateFrom, '<' => $dateTo],
        ]);

        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([[
            'id' => $bettingTournament->id,
            'name' => $bettingTournament->name,
            'sport' => $bettingSport->name,
            'country' => $bettingTournament->country_code,
            'start_at' => $date,
            'end_at' => $date,
        ]]);
    }
}
