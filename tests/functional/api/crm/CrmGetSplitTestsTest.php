<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\repositories\Refcodes;
use app\back\repositories\SplitTestLogs;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

class CrmGetSplitTestsTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testGetUsers(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'user_id' => $userId,
        ]);

        $this->sendAPI('crm', 'get/split-tests', [
            'site_id' => ['=' => $siteId],
            'user_id' => ['=' => $userId],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            [
                'user_id' => $userId,
            ]
        ]);
    }

    public function testGetUsersBySlug(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'user_id' => $userId1,
        ]);
        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split2',
            'user_id' => $userId2,
        ]);

        $this->sendAPI('crm', 'get/split-tests', [
            'site_id' => ['=' => $siteId],
            'split_slug' => ['IN' => ['split1', 'split2']],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId1],
            ['user_id' => $userId2],
        ]);
    }

    public function testGetUsersByDate(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('2020-07-08 00:00:01'),
        ]);

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'user_id' => self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable('2020-07-05 00:00:01'),
        ]);

        $this->sendAPI('crm', 'get/split-tests', [
            'site_id' => ['=' => $siteId],
            'date' => ['BETWEEN' => ['2020-07-06 00:00:01', '2020-07-10 13:01:15']],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId],
        ]);
    }

    public function testGetUsersByGroupSlug(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'split_group_slug' => 'g1',
            'user_id' => $userId,
        ]);

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'split_group_slug' => 'g2',
            'user_id' => self::uniqRuntimeId(),
        ]);

        $this->sendAPI('crm', 'get/split-tests', [
            'site_id' => ['=' => $siteId],
            'group_slug' => ['=' => 'g1'],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId],
        ]);
    }

    public function testGetUsersByEventSlug(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'event_slug' => 'e1',
            'user_id' => $userId,
        ]);

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'event_slug' => 'e2',
            'user_id' => self::uniqRuntimeId(),
        ]);

        $this->sendAPI('crm', 'get/split-tests', [
            'site_id' => ['=' => $siteId],
            'event_slug' => ['=' => 'e1'],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId],
        ]);
    }

    public function testGetUsersByRefcodeSlug(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();

        $refcodesRepo = $this->container()->get(Refcodes::class);
        $refcodeId1 = $refcodesRepo->getIdByCode('test1_code');
        $refcodeId2 = $refcodesRepo->getIdByCode('test2_code');

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'user_id' => $userId,
            'refcode_id' => $refcodeId1,
        ]);

        $this->haveRecord(SplitTestLogs::class, [
            'site_id' => $siteId,
            'log_id' => (string) self::uniqRuntimeId(),
            'identity_key' => uniqid('', true),
            'split_slug' => 'split1',
            'user_id' => self::uniqRuntimeId(),
            'refcode_id' => $refcodeId2,
        ]);

        $this->sendAPI('crm', 'get/split-tests', [
            'site_id' => ['=' => $siteId],
            'refcode' => ['PREFIX IN' => ['test1']],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId],
        ]);
    }
}
