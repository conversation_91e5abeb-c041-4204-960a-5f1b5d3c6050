<?php

declare(strict_types=1);

namespace app\tests\functional\uploads;

use app\back\entities\Host;
use app\back\entities\HostInfo;
use app\back\modules\tools\domainsUpload\DomainsUploadForm;
use app\back\repositories\HostInfos;
use app\back\repositories\Hosts;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;
use Yiisoft\Db\Query\Query;

#[CoversClass(DomainsUploadForm::class)]
class DomainsUploadFormTest extends TestCase
{
    use CsvUploadTestTrait;

    public function testGeneralValidation(): void
    {
        $form = $this->container()->get(DomainsUploadForm::class);

        $data = [
            ['host_id' => ''],
        ];
        $this->runCsvValidateAssert('file is empty', $data, $form);
        self::assertEmpty((new Query($this->db()))->from(Hosts::TABLE_NAME)->all());

        $data = [
            ['host_id' => 'Google.com', 'purpose' => 'Search', 'department' => 'Google']
        ];
        $this->runCsvValidateAssert("Line 0: Department Google is not allowed", $data, $form);
        $this->seeRecord(Hosts::class, ['host' => 'google.com',]);

        $data = [
            ['host_id' => 'google.com', 'purpose' => 'Search', 'department' => 'Huffson']
        ];
        $this->runCsvValidateAssert(null, $data, $form);
    }

    public function testProcess(): void
    {
        $form = $this->container()->get(DomainsUploadForm::class);

        /** @var Host $host1 */
        /** @var Host $host2 */
        [$host1, $host2] = $this->haveRecords(Hosts::class, [
            ['host' => 'google.com'],
            ['host' => 'moogle.com'],
        ]);

        // data inserted correctly
        $data = [
            ['host_id' => 'google.com', 'purpose' => 'Search', 'department' => 'Huffson', 'legality' => 'White', 'external_comment' => 'any comment'],
            ['host_id' => 'moogle.com', 'purpose' => 'Hide', 'department' => 'Huffson'],
        ];
        $this->runProcessAssert(['2 of 2 rows affected by changes'], $data, $form);

        $this->seeRecord(HostInfos::class, ['host_id' => $host1->id, 'purpose' => 'Search', 'department' => HostInfo::DEP_HUFFSON, 'legality' => HostInfo::LEG_WHITE, 'external_comment' => 'any comment']);
        $this->seeRecord(HostInfos::class, ['host_id' => $host2->id, 'purpose' => 'Hide', 'department' => HostInfo::DEP_HUFFSON, 'legality' => null, 'external_comment' => null]);

        // data updated, null values doesn't affects data
        $data = [
            ['host_id' => 'google.com', 'purpose' => 'Hide', 'department' => 'Huffson'],
            ['host_id' => 'moogle.com', 'purpose' => 'Search', 'department' => 'Huffson'],
        ];
        $this->runProcessAssert(['2 of 2 rows affected by changes'], $data, $form);

        $this->seeRecord(HostInfos::class, ['host_id' => $host1->id, 'purpose' => 'Hide', 'department' => HostInfo::DEP_HUFFSON, 'legality' => HostInfo::LEG_WHITE, 'external_comment' => 'any comment']);
        $this->seeRecord(HostInfos::class, ['host_id' => $host2->id, 'purpose' => 'Search', 'department' => HostInfo::DEP_HUFFSON, 'legality' => null, 'external_comment' => null]);

        // data updated, (EMPTY) value clears data
        $data = [
            ['host_id' => 'google.com', 'purpose' => 'Hide', 'department' => 'Huffson', 'legality' => '(EMPTY)', 'external_comment' => '(EMPTY)']
        ];
        $this->runProcessAssert(['1 of 1 rows affected by changes'], $data, $form);
        $this->seeRecord(HostInfos::class, ['host_id' => $host1->id, 'purpose' => 'Hide', 'department' => HostInfo::DEP_HUFFSON, 'legality' => null, 'external_comment' => null]);
    }

    public function testDepartmentValidation(): void
    {
        $form = $this->container()->get(DomainsUploadForm::class);
        /** @var Host $host1 */
        $host1 = $this->haveRecord(Hosts::class, ['host' => 'google.com']);

        $data = [
            ['host_id' => 'google.com', 'purpose' => 'Search', 'department' => 'Huffson', 'legality' => 'White', 'external_comment' => 'any comment']
        ];
        $this->runProcessAssert(['1 of 1 rows affected by changes'], $data, $form);
        $this->seeRecord(HostInfos::class, ['host_id' => $host1->id, 'purpose' => 'Search', 'department' => HostInfo::DEP_HUFFSON, 'legality' => HostInfo::LEG_WHITE, 'external_comment' => 'any comment']);

        // modify with null department
        $data[0]['department'] = null;
        $this->runCsvValidateAssert("Line 0: Department is required", $data, $form);

        // clear department
        $data[0]['department'] = '(EMPTY)';
        $this->runCsvValidateAssert("Line 0: Department (EMPTY) is not allowed", $data, $form);

        //changing department
        $data[0]['department'] = 'Welcome partners';
        $this->runCsvValidateAssert("Line 0: Department modification is forbidden", $data, $form);
    }
}
