<?php

declare(strict_types=1);

namespace app\tests\functional\auth;

use app\back\entities\Employee;
use app\back\modules\auth\AuthSignInForm;
use app\back\repositories\Employees;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

class SignInFromTest extends TestCase
{
    use DbTransactionalUnitTrait;

    private const string VALID_EMAIL = '<EMAIL>';
    private const string VALID_PASSWORD = '123123';

    public function testLogin(): void
    {
        $employee = new Employee();

        $employee->email = self::VALID_EMAIL;
        $employee->setPassword(self::VALID_PASSWORD);
        $employee->generateAuthKey();

        $repo = $this->repo(Employees::class);
        $repo->insert($employee);

        /** @var Employee $employee */
        $employee = $this->seeRecord(Employees::class, ['email' => self::VALID_EMAIL]);

        $form = new AuthSignInForm($repo);
        $employee->status = Employee::STATUS_WAIT;
        self::assertTrue((bool) $repo->update($employee, ['status']));
        self::assertNotEmpty($form->validate(['email' => self::VALID_EMAIL, 'password' => self::VALID_PASSWORD]));

        $form = new AuthSignInForm($repo);
        $employee->status = Employee::STATUS_BLOCKED;
        self::assertTrue((bool) $repo->update($employee, ['status']));
        self::assertNotEmpty($form->validate(['email' => self::VALID_EMAIL, 'password' => self::VALID_PASSWORD]));

        $form = new AuthSignInForm($repo);
        $employee->status = Employee::STATUS_ACTIVE;
        self::assertTrue((bool) $repo->update($employee, ['status']));
        self::assertNotEmpty($form->validate(['email' => self::VALID_EMAIL]));
        self::assertNotEmpty($form->validate(['password' => self::VALID_PASSWORD]));
        self::assertNotEmpty($form->validate(['email' => self::VALID_EMAIL, 'password' => 'wrong_password']));
        self::assertNotEmpty($form->validate(['email' => '<EMAIL>', 'password' => self::VALID_PASSWORD]));
        self::assertEmpty($form->validate(['email' => self::VALID_EMAIL, 'password' => self::VALID_PASSWORD]));
    }
}
